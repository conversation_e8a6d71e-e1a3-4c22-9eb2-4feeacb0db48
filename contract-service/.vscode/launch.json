{
  "version": "0.2.0",
  "configurations": [
    {
      "type": "bun",
      "request": "launch",
      "name": "Debug Bun",
      "program": "${file}",
      "args": [
        "--env-file",
        "./.env.dev"
      ],
      "cwd": "${workspaceFolder}",
      "env": {},
      "strictEnv": false,
      "watchMode": false,
      "stopOnEntry": false,
      "noDebug": false,
      "runtime": "/home/<USER>/.bun/bin/bun",
      "runtimeArgs": [],
    },
    {
      "type": "bun",
      "request": "attach",
      "name": "Attach to Bun",
      "url": "ws://localhost:6499/",
    },
  ],
}