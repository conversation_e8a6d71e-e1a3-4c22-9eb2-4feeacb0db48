# Etapa 1: Construção
FROM node:20-slim AS builder
WORKDIR /app
# Instalar OpenSSL 3.0
RUN apt-get update && apt-get install -y \
    openssl \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*
# Copiar arquivos de configuração
COPY package*.json ./
COPY prisma ./prisma/
COPY tsconfig.json ./
# Instalar dependências e gerar Prisma
RUN npm install
RUN npx prisma generate
# Copiar e compilar código
COPY src ./src/
RUN npm run build

# Etapa 2: Execução
FROM node:20-slim AS runner
WORKDIR /app
# Instalar OpenSSL 3.0 e ferramentas de healthcheck
RUN apt-get update && apt-get install -y \
    openssl \
    curl \
    wget \
    && rm -rf /var/lib/apt/lists/*
# Criar usuário não-root (modified for Debian)
RUN groupadd -r appgroup && useradd -r -g appgroup appuser
# Copiar artefatos necessários
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/prisma ./prisma
# Ajustar permissões
RUN chown -R appuser:appgroup /app
USER appuser
EXPOSE 3000
# Configurar variável de ambiente para o Prisma
ENV PRISMA_QUERY_ENGINE_BINARY=/app/node_modules/@prisma/engines/libquery_engine-debian-openssl-3.0.x.so.node
CMD ["node", "dist/main/index.js"]