import type { IQueueGateway } from '@/domain/gateways'
import type { Either } from '@/domain/shared'
import { left, right } from '@/domain/shared'

export interface IncomeReportQueueJobData {
  investorId: string
  year: number
}

export class RequestIncomeReportUseCase {
  constructor(private queueGateway: IQueueGateway) {}

  async execute(
    year: number,
    investorIds: string[]
  ): Promise<Either<Error, void>> {
    try {
      for (const investorId of investorIds) {
        const jobData: IncomeReportQueueJobData = {
          investorId,
          year,
        }

        await this.queueGateway.add('income-report-queue', {
          data: jobData,
          opts: {
            attempts: 3,
            backoff: {
              type: 'exponential',
              delay: 1000,
            },
            removeOnComplete: true,
          },
        })
      }

      return right(undefined)
    } catch (error) {
      return left(error as Error)
    }
  }
}
