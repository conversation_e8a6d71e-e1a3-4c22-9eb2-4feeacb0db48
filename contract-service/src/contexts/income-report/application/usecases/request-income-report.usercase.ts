import type { IncomeReportQueueJobData } from '@/application/usecases'
import { File } from '@/domain/entities/files'
import type { IEmailGateway } from '@/domain/gateways/email-send-gateway'
import type { IReportGateway } from '@/domain/gateways/report.gateway'
import type { IStorageGateway } from '@/domain/gateways/storage.gateway'
import type { IFileUploadRepository } from '@/domain/repositories/file-upload.repository'
import { type Either, left, right } from '@/domain/shared'
import {
  EmailSendStatus,
  type IncomeReportEmailPayload,
} from '../../domain/entities/email-income-report'
import {
  type IncomeReport,
  IncomeReportStatus,
} from '../../domain/entities/income-report'
import type { Investor } from '../../domain/entities/investor'
import type { ContractRepository } from '../../domain/repositories/contract-repository'
import type { EmailIncomeReportRepository } from '../../domain/repositories/email-income-report.respository'
import type { IncomeRepositoryRepository } from '../../domain/repositories/income-report.repository'
import type { InvestorRepository } from '../../domain/repositories/investor-repository'
import { ContractIncomeCalculator } from '../../domain/services/contract-income-calculator'

export interface IncomeReportDetailsDTO {
  year: number
  broker: string
  advisor: {
    name: string
  }[]
  investor: {
    name: string
    email: string
    document: string
    address: {
      street: string
      number: string
      city: string
      state: string
      zipCode: string
    }
    bankData: {
      bank: string
      agency: string
      account: string
    }
    representative?: {
      name: string
      document: string
    }
  }
  contracts: {
    contractId: string
    baseRate: number
    totalYield: number
    payments: {
      month: number
      date: string
      grossAmount: number
      netAmount: number
    }[]
  }[]
  totalAnnualYield: number
}

export class RequestIncomeReportUseCase {
  constructor(
    private readonly contractRepo: ContractRepository,
    private readonly investorRepo: InvestorRepository,
    private readonly incomeReportRepo: IncomeRepositoryRepository,
    private readonly generateIncomeReportApi: IReportGateway,
    private readonly storageGateway: IStorageGateway,
    private readonly fileUploadRepository: IFileUploadRepository,
    private readonly emailIncomeReportRepo: EmailIncomeReportRepository,
    private readonly emailGateway: IEmailGateway
  ) {}

  async execute({ investorId, year }: IncomeReportQueueJobData): Promise<void> {
    const investor = await this.investorRepo.findById(investorId)
    if (!investor) return

    const contracts = await this.contractRepo.findByInvestorId(investorId)

    const incomeReportExisting = await this.contractRepo.findByIncomeReportId(
      investorId,
      year
    )
    let totalAnnualYield = 0
    let totalBaseValue = 0

    const contractReports = contracts.map(contract => {
      const calculator = new ContractIncomeCalculator(contract)
      const { totalYield, addendTotalAmount, payments, baseValue } =
        calculator.calculateForYear(year)

      totalAnnualYield += totalYield
      totalBaseValue = contract.initialAmount + addendTotalAmount

      return {
        contractId: contract.id,
        baseRate: contract.baseRate,
        totalYield,
        baseValue: baseValue,
        valueAddendum: addendTotalAmount,
        payments: payments.map(p => ({
          month: p.month,
          date: p.date.toISOString(),
          grossAmount: p.grossAmount,
          netAmount: p.netAmount,
          addendsAmount: p.addendsAmount,
          baseValue: p.baseValue,
        })),
      }
    })

    const incomeReport: IncomeReport = incomeReportExisting
      ? incomeReportExisting
      : {
          id: crypto.randomUUID(),
          investor_id: investor.id,
          reference_year: year.toString(),
          status: IncomeReportStatus.DRAFT,
        }

    if (!incomeReportExisting) {
      await this.incomeReportRepo.save(incomeReport)
      await this.incomeReportRepo.connectionContractToIncomeReport(
        incomeReport,
        contracts
      )
    }

    incomeReport.status = IncomeReportStatus.SENDING_TO_REPORT_API
    await this.incomeReportRepo.update(incomeReport)

    const representative = investor.representative
      ? investor.representative
      : investor

    const docFile = await this.generateIncomeReportApi.generateIncomeReport({
      amount: totalAnnualYield,
      applicationBalance: totalBaseValue,
      calendarYear: year.toString(),
      employee: {
        name: representative.name,
        document: representative.document,
        bankAccount: {
          account: contracts[0].investor.bankData.account,
          agency: contracts[0].investor.bankData.agency,
        },
      },
    })
    
    if (docFile.isLeft()) {
      incomeReport.status = IncomeReportStatus.ERROR_REPORT_API
      await this.incomeReportRepo.update(incomeReport)
      return
    }

    incomeReport.status = IncomeReportStatus.REPORT_CREATED
    await this.incomeReportRepo.update(incomeReport)

    const file = await this.generateFilePayload(investor, docFile.value)

    if (file.isLeft()) {
      incomeReport.status = IncomeReportStatus.ERROR_UPLOAD_REPORT_FILE
      await this.incomeReportRepo.update(incomeReport)
      return
    }

    await this.fileUploadRepository.save(file.value)

    incomeReport.file_id = file.value.id
    await this.incomeReportRepo.update(incomeReport)

    const email: IncomeReportEmailPayload = {
      body: {
        to_email: investor.email,
        template_data: {
          calendar_year: year,
          download_link: file.value.url,
          email: investor.email,
          name: investor.name,
          telephone: '',
        },
      },
      from: investor.email,
      id: crypto.randomUUID(),
      status: EmailSendStatus.SENDING,
    }

    const createEmail = await this.emailIncomeReportRepo.save(
      {
        body: email.body,
        from: email.from,
        id: email.id,
        status: email.status,
        error_message: email.error_message,
        retry: email.from,
      },
      incomeReport.id
    )

    if (createEmail.isLeft()) {
      return
    }

    const sendEmail = await this.emailGateway.sendIncomeReportEmail(email)
 
    if (sendEmail.isLeft()) {
      email.status = EmailSendStatus.FAILED
      await this.emailIncomeReportRepo.update(email)
      return
    }
    email.external_id = sendEmail.value.id
    email.status = EmailSendStatus.SUCCESS
    await this.emailIncomeReportRepo.update(email)
  }

  /**
   * Gera um payload de arquivo para o relatório de investimento.
   *
   * @param {IncomeReport} report - O relatório de investimento associado ao arquivo.
   * @param {Buffer} reportFile - O conteúdo do arquivo do relatório em formato Buffer.
   * @returns {Promise<Either<Error, File>>} - Retorna um Either contendo um erro em caso de falha
   * ou uma instância da entidade File com os dados do arquivo gerado.
   *
   * @description
   * - O nome do investidor é formatado para ser usado como parte do nome do arquivo.
   * - O arquivo é enviado ao `storageGateway`, que retorna a URL do arquivo.
   * - Se o upload falhar, retorna um erro (`left`).
   * - Caso contrário, cria uma instância de `File` com o nome gerado, tipo `pdf` e URL retornada.
   */
  private async generateFilePayload(
    investor: Investor,
    reportFile: Buffer
  ): Promise<Either<Error, File>> {
    const nameInvestorTrim = investor.name.replace(/\s+/g, '_')

    const path = `income_report_${nameInvestorTrim}_${Date.now()}`
    const mimeType = 'application/pdf'
    const bufferFile = Buffer.from(reportFile)

    const urlFileResult = await this.storageGateway.uploadFile(
      path,
      bufferFile,
      mimeType
    )

    if (urlFileResult.isLeft()) return left(urlFileResult.value)

    const file = File.create(
      {
        filename: path,
        type: 'pdf',
        url: urlFileResult.value,
      },
      crypto.randomUUID()
    )

    return right(file)
  }
}
