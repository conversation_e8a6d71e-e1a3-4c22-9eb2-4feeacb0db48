import type { MonthlyPayment } from '../value-objects/monthly-payment';
import type { Addendum } from './addendum';
import type { Investor } from './investor';
import type { Participant } from './participant';

// Constante para definir o limite máximo de dias considerados em cada período
const MAX_DAYS_IN_PERIOD = 30;

/**
 * Classe que representa um contrato financeiro, encapsulando as regras de cálculo dos pagamentos mensais.
 */
export class Contract {
  constructor(
    public readonly id: string,
    public readonly investor: Investor,
    public readonly broker: Participant,
    public readonly advisors: Participant[],
    public readonly baseRate: number,
    public readonly startDate: Date,
    private readonly addenda: Addendum[],
    public readonly initialAmount: number,
    public readonly proofPaymentUrl: string,
    public readonly contractUrl: string,
    public readonly endDate: Date,
  ) {}

  /**
   * Gera os pagamentos mensais para um determinado ano.
   * @param year - O ano para o qual os pagamentos mensais serão gerados.
   * @returns Um array de objetos MonthlyPayment com os detalhes de cada pagamento.
   */
  public generateMonthlyPayments(year: number): MonthlyPayment[] {
    const payments: MonthlyPayment[] = [];

    console.log(
      `Iniciando geração dos pagamentos mensais para o ano ${year}.\n`,
    );
    for (let month = 0; month < 12; month++) {
      const paymentDate = this.calculatePaymentDate(year, month);
      console.log(
        `Mês ${
          month + 1
        }: Data de pagamento calculada: ${paymentDate.toISOString()}`,
      );

      // Verifica se deve pular a criação do pagamento
      if (
        paymentDate.getMonth() === this.startDate.getMonth() &&
        this.startDate.getFullYear() === year
      ) {
        console.log(
          `Mês ${month + 1}: Ignorado, pois é o mês de início do contrato.\n`,
        );
        continue;
      }
      if (paymentDate < this.startDate) {
        console.log(
          `Mês ${
            month + 1
          }: Ignorado, pois a data de pagamento (${paymentDate.toISOString()}) é anterior à data de início do contrato (${this.startDate.toISOString()}).\n`,
        );
        continue;
      }

      const previousPaymentDate = this.calculatePreviousPaymentDate(
        year,
        month,
      );
      console.log(
        `Mês ${
          month + 1
        }: Data do pagamento anterior: ${previousPaymentDate.toISOString()}`,
      );

      const daysInPeriod = this.calculateDaysInPeriod(
        paymentDate,
        previousPaymentDate,
      );
      console.log(`Mês ${month + 1}: Dias no período: ${daysInPeriod}`);

      const baseValue = this.initialAmount;

      // Cálculo do valor proveniente dos aditivos (addenda) para o mês corrente
      const valueFromAddenda = this.calculateAddendumValueForMonth(
        month,
        year,
        paymentDate,
      );
      console.log(`Mês ${month + 1}: Valor dos aditivos: ${valueFromAddenda}`);

      const totalMonthValue =
        (baseValue * (this.baseRate / 100) * daysInPeriod) /
          MAX_DAYS_IN_PERIOD +
        valueFromAddenda;
      console.log(
        `Mês ${month + 1}: Valor total do pagamento: ${totalMonthValue}\n`,
      );

      payments.push({
        month: month + 1,
        year,
        date: paymentDate,
        grossAmount: totalMonthValue,
        netAmount: totalMonthValue,
        addendsAmount: valueFromAddenda,
        baseValue: baseValue,
        valueAddendum: valueFromAddenda,
      });

      // Espaço de linha para separar os logs de cada mês
      console.log('');
    }

    console.log(`Geração de pagamentos concluída para o ano ${year}.\n`);
    return payments;
  }

  /**
   * Calcula a data de pagamento para um determinado mês e ano, baseado na data de início do contrato.
   * @param year - O ano do pagamento.
   * @param month - O mês (0-indexado) do pagamento.
   * @returns A data de pagamento calculada.
   */
  private calculatePaymentDate(year: number, month: number): Date {
    const date = new Date(year, month, this.startDate.getDate() + 1);

    return date;
  }

  /**
   * Calcula a data do pagamento anterior para o mês corrente.
   * @param year - O ano do pagamento.
   * @param month - O mês (0-indexado) corrente.
   * @returns A data do pagamento anterior.
   */
  private calculatePreviousPaymentDate(year: number, month: number): Date {
    const date = new Date(year, month - 1, this.startDate.getDate() + 1);
    return date;
  }

  /**
   * Calcula a quantidade de dias entre duas datas, limitada ao valor máximo definido.
   * @param currentDate - A data atual de pagamento.
   * @param previousDate - A data do pagamento anterior.
   * @returns O número de dias no período, limitado a MAX_DAYS_IN_PERIOD.
   */
  private calculateDaysInPeriod(currentDate: Date, previousDate: Date): number {
    const diffMilliseconds = currentDate.getTime() - previousDate.getTime();
    const days = Math.ceil(diffMilliseconds / (1000 * 60 * 60 * 24));
    const limitedDays = Math.min(days, MAX_DAYS_IN_PERIOD);
    return limitedDays;
  }

  /**
   * Calcula o valor total proveniente dos aditivos (addenda) para um determinado mês.
   * @param month - O mês (0-indexado) para o qual o cálculo é realizado.
   * @param year - O ano para o qual o cálculo é realizado.
   * @param paymentDate - A data de pagamento referente ao mês.
   * @returns O valor acumulado dos aditivos para o mês.
   */
  private calculateAddendumValueForMonth(
    month: number,
    year: number,
    paymentDate: Date,
  ): number {
    const totalAddendumValue = this.addenda.reduce((sum, addendum) => {
      if (!this.isAddendumRelevantForMonth(addendum, month, year)) {
        console.log(
          `calculateAddendumValueForMonth: Aditivo ignorado para o mês ${
            month + 1
          }.\n`,
        );
        return sum;
      }

      const diffMilliseconds =
        paymentDate.getTime() - addendum.startDate.getTime();
      const daysCovered = Math.min(
        Math.ceil(diffMilliseconds / (1000 * 60 * 60 * 24)),
        MAX_DAYS_IN_PERIOD,
      );
      const proportion = daysCovered / MAX_DAYS_IN_PERIOD;
      const proportionalValue =
        addendum.amount * proportion * (this.baseRate / 100);
      return sum + proportionalValue;
    }, 0);

    return totalAddendumValue;
  }

  /**
   * Verifica se um aditivo (addendum) é relevante para o cálculo do pagamento de um determinado mês e ano.
   * @param addendum - O aditivo a ser verificado.
   * @param month - O mês (0-indexado) em questão.
   * @param year - O ano em questão.
   * @returns Verdadeiro se o aditivo estiver ativo durante o mês; caso contrário, falso.
   */
  private isAddendumRelevantForMonth(
    addendum: Addendum,
    month: number,
    year: number,
  ): boolean {
    const monthStart = new Date(year, month, 1);
    const monthEnd = new Date(year, month + 1, 0);
    const relevant = !(
      addendum.endDate < monthStart || addendum.startDate > monthEnd
    );
    return relevant;
  }

  getAddensTotalAmount(year: number) {
    return this.addenda.reduce((acc, curr) => {
      if (!this.isYearWithinContract(year, curr.startDate, curr.endDate)) {
        return 0;
      }
      return acc + curr.amount;
    }, 0);
  }

  getBaseValueContract(year: number) {
    if (this.isYearWithinContract(year, this.startDate, this.endDate)) {
      return this.initialAmount;
    }
    return 0;
  }

  isYearWithinContract(
    year: number,
    contractStart: Date,
    contractEnd: Date,
  ): boolean {
    const startYear = contractStart.getFullYear();
    const endYear = contractEnd.getFullYear();

    return year >= startYear && year <= endYear;
  }
}
