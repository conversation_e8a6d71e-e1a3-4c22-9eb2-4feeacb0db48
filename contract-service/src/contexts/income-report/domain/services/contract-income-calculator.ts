import type { Contract } from '../entities/contract';
import type { MonthlyPayment } from '../value-objects/monthly-payment';

export class ContractIncomeCalculator {
  constructor(private readonly contract: Contract) {}

  public calculateForYear(year: number): {
    totalYield: number;
    payments: MonthlyPayment[];
    baseValue: number;
    addendTotalAmount: number;
  } {
    const payments = this.contract.generateMonthlyPayments(year);
    const totalYield = payments.reduce(
      (acc, payment) => acc + payment.netAmount,
      0,
    );

    const baseValue = this.contract.getBaseValueContract(year);

    return {
      totalYield,
      payments,
      baseValue: baseValue,
      addendTotalAmount: this.contract.getAddensTotalAmount(year),
    };
  }
}
