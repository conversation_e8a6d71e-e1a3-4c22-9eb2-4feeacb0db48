import type { Investor } from '@/contexts/income-report/domain/entities/investor'
import type { InvestorRepository } from '@/contexts/income-report/domain/repositories/investor-repository'
import prisma from '@/infrastructure/prisma/client'

export class PrismaInvestorRepository implements InvestorRepository {
  async findById(id: string): Promise<Investor | null> {
    const investor = await prisma.owner_role_relation.findFirst({
      where: {
        id,
        role: {
          name: 'investor',
        },
      },
      include: {
        owner: { include: { address: true } },
        business: { include: { address: true } },
      },
    })

    if (!investor) return null

    if (investor.owner) {
      const investorAddress = investor.owner.address[0]

      return {
        id: investor.id,
        document: investor.owner.cpf,
        address: {
          street: investorAddress.street,
          number: investorAddress.number,
          city: investorAddress.city,
          state: investorAddress.state,
          zipCode: investorAddress.cep,
        },
        type: 'PF',
        name: investor.owner.name,
        email: investor.owner.email,
        bankData: { account: '', agency: '', bank: '', pixKey: '' },
      }
    }

    const business = investor.business

    if (!business) {
      return null
    }

    if (!business.owner_id) {
      return null
    }
    const representative = await prisma.owner.findFirst({
      where: { id: business.owner_id },
      include: { address: true },
    })

    if (!representative) {
      return null
    }

    const companyAddress = business.address[0]
    const representativeAddress = representative.address[0]

    return {
      id: investor.id,
      document: business.cnpj,
      address: {
        street: companyAddress.street,
        number: companyAddress.number,
        city: companyAddress.city,
        state: companyAddress.state,
        zipCode: companyAddress.cep,
      },
      representative: {
        name: representative.name,
        document: representative.cpf,
        email: representative.email,
        address: {
          street: representativeAddress.street,
          number: representativeAddress.number,
          city: representativeAddress.city,
          state: representativeAddress.state,
          zipCode: representativeAddress.cep,
        },
        bankData: { account: '', agency: '', bank: '', pixKey: '' },
      },
      type: 'PJ',
      name: business.fantasy_name,
      email: business.email,
      bankData: { account: '', agency: '', bank: '', pixKey: '' },
    }
  }
}
