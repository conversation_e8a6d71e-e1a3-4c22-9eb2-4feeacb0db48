import { ContractStatus } from '@/domain/entities/contracts'
import type { IQueueGateway } from '@/domain/gateways'
import type { IInvestmentContractRepository } from '@/domain/repositories'
import env from '@/main/config/env'
import cron from 'node-cron'

export class AwaitingInvestorSignature {
  constructor(
    private readonly investorRepository: IInvestmentContractRepository,
    private queueGateway: IQueueGateway
  ) {}

  start() {
    cron.schedule(env.AWAITING_INVESTOR_SIGNATURE_CRON, async () => {
      const contracts = await this.investorRepository.findByStatus(
        ContractStatus.AWAITING_INVESTOR_SIGNATURE
      )
      for (const contract of contracts) {
        await this.queueGateway.add('process-investor-signature', {
          data: contract,
          opts: {
            attempts: 3,
            backoff: {
              type: 'exponential',
              delay: 1000,
            },
            removeOnComplete: true,
          },
        })
      }
    })
  }
}
