
import { GenerateContractPdfHandler } from '@/application/handlers/contracts/generate-contract-pdf.handler';
import { HandleContractExpiration } from '@/application/handlers/contracts/handle-contract-expiration.handler';
import { HandleDepositProofUploaded } from '@/application/handlers/contracts/handle-deposit-proof-uploaded.handler';
import { SendContractToSignatureHandler } from '@/application/handlers/contracts/send-contract-to-signature.handler';
import { CreateInvestorCredentialsHandler } from '@/application/handlers/investor/create-investor-credentials.handler';
import { UploadContractFilesHandler } from '@/application/handlers/contracts/upload-contract-files.handler'
import type { ContractCreatedEvent } from '@/domain/events/contracts/contract-created.event';
import type { ContractExpiredEvent } from '@/domain/events/contracts/contract-expired.event';
import type { ContractPdfGeneratedEvent } from '@/domain/events/contracts/contract-pdf-generated.event';
import type { DepositProofUploadedEvent } from '@/domain/events/contracts/deposit-proof-uploaded.event';
import type { CreateInvestorCredentialsEvent } from '@/domain/events/investor/create-investor-credentials.event';
import type { ContractFilesUploadEvent } from '@/domain/events/contracts/contract-files-upload.event'
import { DomainEventMediator } from '@/domain/shared';
import { AwsStorageGateway } from '@/main/adapters/aws-storage.adapter';
import { ContractGeneratorAdapter } from '@/main/adapters/contract-generator-api/contract-generator.adapter';
import { PinoLoggerAdapter } from '@/main/adapters/pino-logger.adapter';
import { SignatureAdapter } from '@/main/adapters/rbm/signature.adapter';
import env from '@/main/config/env';
import { FetchHttpClient } from '../http/fetch-http-client';
import { WelcomeEmailAdapter } from '@/main/adapters/welcome-email.adapter';
import { PrismaInvestmentContractRepository } from '../prisma/repositories';
import { PrismaInvestorRepository } from '../prisma/repositories/investor.repository';


const contractRepo = new PrismaInvestmentContractRepository();
const investorRepo = new PrismaInvestorRepository();
const fetchHttpClient = new FetchHttpClient();
const rmbApi = new SignatureAdapter(fetchHttpClient);
const contractGenerator = new ContractGeneratorAdapter();
const pinoLoggerAdapter = new PinoLoggerAdapter();
const welcomeEmailAdapter = new WelcomeEmailAdapter(pinoLoggerAdapter);

const awsS3StorageGateway = new AwsStorageGateway(
  env.AWS_BUCKET_NAME,
  env.AWS_REGION
);

DomainEventMediator.register(
  'ContractCreated',
  async (event: ContractCreatedEvent) =>
    new GenerateContractPdfHandler(
      contractRepo,
      contractGenerator,
      awsS3StorageGateway,
      pinoLoggerAdapter
    ).handle(event)
);

// Evento: PDF gerado com sucesso
DomainEventMediator.register(
  'ContractPdfGenerated',
  async (event: ContractPdfGeneratedEvent) =>
    new SendContractToSignatureHandler(
      contractRepo,
      rmbApi,
      pinoLoggerAdapter
    ).handle(event)
);

// Evento: Assinatura expirou
DomainEventMediator.register(
  'ContractExpired',
  async (event: ContractExpiredEvent) =>
    new HandleContractExpiration(contractRepo).handle(event)
);

// Evento: Comprovante de depósito enviado
DomainEventMediator.register(
  'DepositProofUploaded',
  async (event: DepositProofUploadedEvent) =>
    new HandleDepositProofUploaded(contractRepo).handle(event)

);

// Evento: Criar credenciais do investidor
DomainEventMediator.register(
  'CreateInvestorCredentials',
  async (event: CreateInvestorCredentialsEvent) => {
    new CreateInvestorCredentialsHandler(
      contractRepo,
      investorRepo,
      welcomeEmailAdapter,
      pinoLoggerAdapter
    ).handle(event);
  }
);


// Evento: Upload de arquivos de contrato existente
DomainEventMediator.register(
  'ContractFilesUpload',
  async (event: ContractFilesUploadEvent) => {
    await new UploadContractFilesHandler(
      awsS3StorageGateway,
      contractRepo,
      pinoLoggerAdapter
    ).handle(event)
  }
)

