import { Company } from "@/domain/entities/parties";
import { type Either, left, right } from "@/domain/shared";
import { Cnpj, Email } from "@/domain/value-objects";
import { Address } from "@/domain/value-objects/address.value-object";
import { CompanyType } from "@/domain/value-objects/company-type.value-object";
import { Phone } from "@/domain/value-objects/phone.value-object";
import type { Prisma } from "@prisma/client";
import { IndividualMapper } from "./individual.mapper";

export type BusinessWithRepresentativeAndAddress = Prisma.businessGetPayload<{
  include: {
    address: true;
    owner_business_relation: {
      include: {
        owner: {
          include: {
            address: true;
          };
        };
      };
    };
  };
}>;

export class BusinessMapper {
  static toDomain(
    raw: BusinessWithRepresentativeAndAddress
  ): Either<Error, Company> {
    // Debug: Log dos dados brutos da empresa
    console.log("🔍 BusinessMapper - Dados brutos da empresa:", {
      id: raw.id,
      company_name: raw.company_name,
      fantasy_name: raw.fantasy_name,
      cnpj: raw.cnpj,
      email: raw.email,
    });

    // CNPJ, email, phone
    const cnpj = Cnpj.create(raw.cnpj);
    const email = Email.create(raw.email);
    const phone = Phone.create("***********"); // ⚠️ placeholder

    if (cnpj.isLeft()) return left(cnpj.value);
    if (email.isLeft()) return left(email.value);
    if (phone.isLeft()) return left(phone.value);

    // Address da empresa
    const rawAddress = raw.address?.[0];

    let address = Address.create("", "", "", "", "", "", "");

    if (rawAddress) {
      address = Address.create(
        rawAddress.street,
        rawAddress.number,
        rawAddress.city,
        rawAddress.state,
        rawAddress.cep,
        rawAddress.neighborhood,
        rawAddress.complement ?? undefined
      );
    }

    if (address.isLeft()) return left(address.value);

    const representative = raw.owner_business_relation[0];

    if (!representative) {
      return left(
        new Error(
          `Falta representante legal para a empresa. Business ID: ${raw.id}, CNPJ: ${raw.cnpj}. owner_business_relation count: ${raw.owner_business_relation.length}`
        )
      );
    }

    const legalRepResult = IndividualMapper.toDomain(representative.owner);
    if (legalRepResult.isLeft()) return left(legalRepResult.value);

    const company = new Company(
      raw.id,
      raw.company_name,
      email.value,
      phone.value,
      cnpj.value,
      legalRepResult.value,
      address.value,
      CompanyType.ltda(),
      raw.size || ""
    );

    // Debug: Log da empresa mapeada
    console.log("🔍 BusinessMapper - Empresa mapeada:", {
      id: company.id,
      name: company.getName(),
      cnpj: company.getCnpj(),
    });

    return right(company);
  }
}
