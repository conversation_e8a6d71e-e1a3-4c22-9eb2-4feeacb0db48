import { Broker } from '@/domain/entities/user'
import { type Either, left, right } from '@/domain/shared'
import type { Prisma } from '@prisma/client'
import { BusinessMapper } from './company.mapper'
import { IndividualMapper } from './individual.mapper'

export type BrokerOwnerRoleRelationWithFullParty =
  Prisma.owner_role_relationGetPayload<{
    include: {
      owner: {
        include: { address: true; account: true }
      }
      business: {
        include: {
          address: true
          account: true
          owner_business_relation: {
            include: {
              owner: {
                include: { address: true }
              }
            }
          }
        }
      }
    }
  }>

export class BrokerMapper {
  static toDomain(
    raw: BrokerOwnerRoleRelationWithFullParty
  ): Either<Error, Broker> {
    if (!raw) return left(new Error('Empty broker relation'))


    if (raw.owner) {
      const result = IndividualMapper.toDomain(raw.owner)
      if (result.isLeft()) return left(result.value)
      return right(Broker.createFromIndividual(result.value, raw.id))
    }

    if (raw.business) {
      const result = BusinessMapper.toDomain(raw.business)
      if (result.isLeft()) return left(result.value)
      return right(Broker.createFromCompany(result.value, raw.id))
    }

    return left(new Error('Broker must have either owner or business defined'))
  }
}
