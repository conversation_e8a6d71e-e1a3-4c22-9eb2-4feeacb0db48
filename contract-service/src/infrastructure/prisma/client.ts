import { Prisma, PrismaClient } from '@prisma/client'

declare global {
  var prismaClient: PrismaClient | undefined
}

const prismaClientSingleton = () => {
  return new PrismaClient({
    transactionOptions: {
      isolationLevel: Prisma.TransactionIsolationLevel.Serializable,
      maxWait: 5000,
      timeout: 20000,
    },
  })
}

const prisma = globalThis.prismaClient ?? prismaClientSingleton()

if (process.env.NODE_ENV !== 'production') {
  globalThis.prismaClient = prisma
}

export default prisma
