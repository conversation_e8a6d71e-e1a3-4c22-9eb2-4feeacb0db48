import type { ITransactionContext } from '@/application/interfaces'
import type { <PERSON><PERSON><PERSON> } from '@/domain/entities/user'
import type { IBrokerRepository } from '@/domain/repositories'
import { type Either, left, right } from '@/domain/shared'
import { logger } from '@/infrastructure/pino/logger'
import type { Prisma } from '@prisma/client'
import prisma from '../client'
import { BrokerMapper } from '../mappers/broker.mapper'

export function hasGetClient(
  tx: ITransactionContext
): tx is ITransactionContext & { getClient: () => Prisma.TransactionClient } {
  return 'getClient' in tx && typeof tx.getClient === 'function'
}
export class PrismaBrokerRepository implements IBrokerRepository {
  save(entity: Broker): Promise<Either<Error, void>> {
    throw new Error('Method not implemented.')
  }
  findAll(
    filters?: unknown,
    page?: number,
    limit?: number
  ): Promise<Either<Error, Broker[]>> {
    throw new Error('Method not implemented.')
  }
  delete(id: string): Promise<Either<Error, void>> {
    throw new Error('Method not implemented.')
  }
  async findById(
    id: string,
    tx?: ITransactionContext
  ): Promise<Either<Error, string | null>> {
    try {
      const client = tx && hasGetClient(tx) ? tx.getClient() : prisma
      const raw = await client.owner_role_relation.findUnique({
        where: { id },
      })

      return right(raw?.id ?? null)
    } catch (err) {
      return left(new Error(`Erro ao buscar broker: ${(err as Error).message}`))
    }
  }
}
