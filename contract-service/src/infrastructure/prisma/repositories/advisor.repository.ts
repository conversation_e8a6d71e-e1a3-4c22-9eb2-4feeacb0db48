import type { ITransactionContext } from '@/application/interfaces'
import type { Advisor } from '@/domain/entities/user'
import type { IAdvisorRepository } from '@/domain/repositories/advisor.repository'
import { type Either, left, right } from '@/domain/shared/either'
import prisma from '../client'
import { AdvisorMapper } from '../mappers/advisor.mapper'
import { hasGetClient } from './broker.repository'

export class PrismaAdvisorRepository implements IAdvisorRepository {
  save(entity: Advisor): Promise<Either<Error, void>> {
    throw new Error('Method not implemented.')
  }
  findAll(
    filters?: unknown,
    page?: number,
    limit?: number
  ): Promise<Either<Error, Advisor[]>> {
    throw new Error('Method not implemented.')
  }
  delete(id: string): Promise<Either<Error, void>> {
    throw new Error('Method not implemented.')
  }

  async findById(
    id: string,
    tx?: ITransactionContext
  ): Promise<Either<Error, string | null>> {
    try {
      const client = tx && hasGetClient(tx) ? tx.getClient() : prisma
      const advisorDb = await client.owner_role_relation.findUnique({
        where: { id },
      })

      if (!advisorDb) return left(new Error('Assessor não encontrado'))

      return right(advisorDb.id ?? null)
    } catch (err) {
      return left(
        new Error(`Erro ao buscar assessor: ' + ${(err as Error).message}`)
      )
    }
  }
}
