import type { InvestmentContract } from '@/domain/entities/contracts';
import type { IncomeReport } from '@/domain/entities/reports';
import type { IIncomeReportRepository } from '@/domain/repositories/income-report.repository';
import { type Either, left, right } from '@/domain/shared';
import prisma from '../client';
import { IncomeReportMapper } from '../mappers';
import { DetailsIncomeReportMapper } from '../mappers/details-income-report.mapper';
export class PrismaIncomeReportRepository implements IIncomeReportRepository {
  async save(report: IncomeReport): Promise<void> {
    const mapperIncomeReport = IncomeReportMapper.toPersistence(report);

    await prisma.income_report.create({ data: mapperIncomeReport });
  }

  async update(report: IncomeReport): Promise<void> {
    const mapperIncomeReport = IncomeReportMapper.toPersistence(report);

    await prisma.income_report.update({
      where: { id: mapperIncomeReport.id },
      data: mapperIncomeReport,
    });
  }

  findByInvestorAndYear(
    investorId: string,
    year: number,
  ): Promise<IncomeReport | null> {
    throw new Error('Method not implemented.');
  }

  async connectionContractToIncomeReport(report: IncomeReport): Promise<void> {
    const contracts = report.getContracts();

    await Promise.all(
      contracts.map((contract) =>
        prisma.income_reports_contracts.create({
          data: {
            contract_id: contract.contract.id,
            income_report_id: report.id,
          },
        }),
      ),
    );
  }
  async findDetailsIncomeReport(
    investorId: string,
    year: number,
  ): Promise<Either<Error, InvestmentContract[]>> {
    const contractList: InvestmentContract[] = [];

    const contracts = await prisma.contract.findMany({
      where: {
        investor_id: investorId,
        status: 'ACTIVE',
        start_contract: { gte: new Date(year, 0, 1) },
      },
      include: {
        pre_register: true,
        contract_advisor: {
          include: {
            owner_role_relation: {
              include: {
                owner: {
                  include: { address: true },
                },
                business: {
                  include: {
                    address: true,
                    owner_business_relation: {
                      include: {
                        owner: {
                          include: { address: true },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
        owner_role_relation_contract_investor_idToowner_role_relation: {
          include: {
            pre_register: true,
            income_report: {
              include: {
                files: true,
              },
            },
            owner: {
              include: {
                address: true,
              },
            },
            business: {
              include: {
                address: true,

                owner_business_relation: {
                  include: {
                    owner: {
                      include: { address: true },
                    },
                  },
                },
              },
            },
          },
        },
        owner_role_relation_contract_owner_role_relationToowner_role_relation: {
          include: {
            owner: {
              include: { address: true },
            },
            business: {
              include: {
                address: true,
                owner_business_relation: {
                  include: {
                    owner: {
                      include: { address: true },
                    },
                  },
                },
              },
            },
          },
        },
        addendum: {
          include: {
            income_payment_scheduled_addendum: {
              include: {
                income_payment_scheduled: {
                  select: {
                    scheduled_date: true,
                  },
                },
              },
            },
          },
          where: {
            status: 'FULLY_SIGNED',
          },
        },
      },
    });

    if (contracts.length === 0) {
      return right(contractList);
    }
    for (const contract of contracts) {
      const investmentContract = DetailsIncomeReportMapper.toDomain(contract);
      if (investmentContract.isRight()) {
        contractList.push(investmentContract.value);
      } else {
        return left(investmentContract.value);
      }
    }

    return right(contractList);
  }
}
