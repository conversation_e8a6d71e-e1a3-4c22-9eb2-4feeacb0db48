import type { <PERSON>ggerGateway } from '@/domain/gateways/logger.gateway';
import type { EmailData, IEmailRepository } from '@/domain/repositories/email.repository';
import { type Either, left, right } from '@/domain/shared';
import { PinoLoggerAdapter } from '@/main/adapters/pino-logger.adapter';
import prisma from '../client';

export class PrismaEmailRepository implements IEmailRepository {
  private readonly logger: LoggerGateway;

  constructor() {
    this.logger = new PinoLoggerAdapter();
  }

  async create(emailData: EmailData): Promise<Either<Error, void>> {
    try {
      await prisma.email.create({
        data: {
          id: emailData.id,
          from: emailData.from,
          reply: emailData.to,
          body: emailData.body,
          body_type: emailData.bodyType,
          status: emailData.status,
          error_message: emailData.errorMessage,
          external_id: emailData.externalId,
        },
      });

      return right(undefined);
    } catch (error) {
      this.logger.error(`Erro ao salvar e-mail no banco de dados: ${error}`);
      return left(error instanceof Error ? error : new Error(String(error)));
    }
  }

  async updateExternalId(emailId: string, externalId: string): Promise<Either<Error, void>> {
    try {
      await prisma.email.update({
        where: { id: emailId },
        data: {
          external_id: externalId,
        },
      });

      return right(undefined);
    } catch (error) {
      this.logger.error(`Erro ao atualizar external_id do e-mail no banco de dados: ${error}`);
      return left(error instanceof Error ? error : new Error(String(error)));
    }
  }
}
