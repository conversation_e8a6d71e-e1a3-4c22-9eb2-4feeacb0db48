import type { IncomeReportEmail } from '@/domain/entities/reports/income-report-email.entity'
import type { IEmailIncomeReportRepository } from '@/domain/repositories/email-income-report.repositories'
import { type Either, left, right } from '@/domain/shared'
import prisma from '../client'

export class PrismaEmailIncomeRepository
  implements IEmailIncomeReportRepository
{
  async save(
    incomeEmail: IncomeReportEmail
  ): Promise<Either<Error, undefined>> {
    try {
      await prisma.email.create({
        data: {
          from: '<EMAIL>',
          body: JSON.stringify(incomeEmail.getEmailBody()),
          body_type: 'json',
          error_message: incomeEmail.getErrorMessage(),
          id: incomeEmail.id,
          status: incomeEmail.getStatus(),
          income_report_email: {
            create: {
              income_report_id: incomeEmail.getIncomeReportId(),
            },
          },
        },
      })

      return right(undefined)
    } catch (error) {
      return left(error as Error)
    }
  }
  async update(incomeEmail: IncomeReportEmail): Promise<void> {
    await prisma.email.update({
      where: {
        id: incomeEmail.id,
      },
      data: {
        error_message: incomeEmail.getErrorMessage(),
        id: incomeEmail.id,
        status: incomeEmail.getStatus(),
        sent_at: incomeEmail.getSentAt(),
      },
    })
  }
}
