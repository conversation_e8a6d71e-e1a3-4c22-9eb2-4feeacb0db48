import type {
  IContractDeletionRepository,
  ContractDeletion,
} from '@/domain/repositories/contract-deletion.repository';
import { type Either, left, right } from '@/domain/shared';
import { PinoLoggerAdapter } from '@/main/adapters/pino-logger.adapter';
import prisma from '../client';
import { hasGetClient } from './broker.repository';
import { ITransactionContext } from '@/application/interfaces';

export class PrismaContractDeletionRepository
  implements IContractDeletionRepository
{
  private readonly logger = new PinoLoggerAdapter();

  async create(
    data: Omit<ContractDeletion, 'id' | 'deletedAt' | 'updatedAt'>,
    tx?: ITransactionContext
  ): Promise<Either<Error, ContractDeletion>> {
    try {
      const client = tx && hasGetClient(tx) ? tx.getClient() : prisma;

      const deletion = await client.contract_deletion.create({
        data: {
          contract_id: data.contractId,
          deleted_by_id: data.deletedById,
          reason: data.reason,
        },
      });

      this.logger.info(`Contrato ${data.contractId} deletado com sucesso`);

      return right({
        id: deletion.id,
        contractId: deletion.contract_id,
        deletedById: deletion.deleted_by_id,
        reason: deletion.reason,
        deletedAt: deletion.deleted_at,
        updatedAt: deletion.updated_at,
      });
    } catch (error) {
      this.logger.error(`Erro ao deletar contrato: ${error}`);
      return left(error as Error);
    }
  }
}
