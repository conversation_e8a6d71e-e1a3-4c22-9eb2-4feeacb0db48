import {
  type ITransactionContext,
  TransactionIsolationLevel,
} from '@/application/interfaces/transaction-context'
import type { IUnitOfWork } from '@/application/interfaces/unit-of-work'
import type { Prisma, PrismaClient } from '@prisma/client'
import { PrismaTransactionContext } from './prisma-transaction-context'

export class PrismaUnitOfWork implements IUnitOfWork {
  constructor(private readonly prisma: PrismaClient) {}

  async runInTransaction<T>(
    work: (tx: ITransactionContext) => Promise<T>
  ): Promise<T> {
    return await this.prisma.$transaction(async tx => {
      const txContext = new PrismaTransactionContext(tx)
      const result = await work(txContext)
      await txContext.commit() // Executa ações pós-commit, se houver
      return result
    })
  }

  async runInTransactionWithIsolation<T>(
    isolationLevel: TransactionIsolationLevel,
    work: (tx: ITransactionContext) => Promise<T>
  ): Promise<T> {
    // O Prisma permite configurar o nível de isolamento via opções da transação,
    // se suportado. Aqui fazemos a conversão para o tipo esperado pelo Prisma.
    return await this.prisma.$transaction(
      async tx => {
        const txContext = new PrismaTransactionContext(tx)
        const result = await work(txContext)
        await txContext.commit()
        return result
      },
      {
        isolationLevel: mapIsolationLevel(isolationLevel),
      }
    )
  }
}

export function mapIsolationLevel(
  level: TransactionIsolationLevel
): Prisma.TransactionIsolationLevel {
  switch (level) {
    case TransactionIsolationLevel.READ_UNCOMMITTED:
      return 'ReadUncommitted'
    case TransactionIsolationLevel.READ_COMMITTED:
      return 'ReadCommitted'
    case TransactionIsolationLevel.REPEATABLE_READ:
      return 'RepeatableRead'
    case TransactionIsolationLevel.SERIALIZABLE:
      return 'Serializable'
    default:
      throw new Error(`Nível de isolamento inválido: ${level}`)
  }
}
