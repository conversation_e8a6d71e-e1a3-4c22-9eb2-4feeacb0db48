import { Queue, type QueueOptions } from 'bullmq'
import type { IQueueGateway, JobTypeGateway, QueueJob } from '@/domain/gateways'

export class BullQ<PERSON>ueAdapter implements IQueueGateway {
  private queue: Queue

  constructor(queueName: string, options?: QueueOptions) {
    this.queue = new Queue(queueName, options)
  }

  async add<T>(jobName: string, job: QueueJob<T>): Promise<void> {
    await this.queue.add(jobName, job.data, job.opts)
  }

  async getJobs<T>(status: JobTypeGateway): Promise<QueueJob<T>[]> {
    const jobs = await this.queue.getJobs(status)

    return jobs.map(job => ({
      id: job.id,
      data: job.data as T,
      opts: job.opts,
    }))
  }

  async removeJob(jobId: string): Promise<void> {
    const job = await this.queue.getJob(jobId)
    if (job) {
      await job.remove()
    }
  }
}
