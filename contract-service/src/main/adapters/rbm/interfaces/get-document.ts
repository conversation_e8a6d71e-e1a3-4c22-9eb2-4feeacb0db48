export interface IDocumentApiResponse {
  erro: boolean;
  message?: string;
  payload: DocumentPayload;
}

export interface DocumentPayload {
  documento: Document;
}

export interface Document {
  id: number;
  empresa_id: number;
  titulo: string;
  titulo_modificado: string;
  numero_externo: string;
  extra_info: string | null;
  extensao: string;
  size_certificado: number | null;
  size_original: number | null;
  path_padronizado: string;
  size_padronizado: number | null;
  path_assinado: string;
  size_assinado: number | null;
  path_p7s_attached: string | null;
  size_p7s_attached: number | null;
  size_com_carimbo: number | null;
  status: string;
  hash: string;
  migrado_s3: number;
  finalizacao_automatica: string;
  dataLimite: string;
  tag: string;
  quantidade_registros: number;
  bloqueio_assinatura_cpf: string | null;
  bloqueio_assinatura_em: string | null;
  titulo_id: number | null;
  created_at: string;
  user: DocumentUser;
  historicos: DocumentHistory[];
  signatarios: Signatory[];
  interessados: InterestedParty[];
  quantidadeAssinaturasRealizadas: number;
  quantidadeAssinaturasSolicitadas: number;
  possuiAssinaturaComCertificadoDigital: boolean;
  ordemAssinaturaUtilizar: boolean;
  possuiDocumentoOriginal: boolean;
  exibirDownloadDocumentoComCarimbo: boolean;
  documentoExpirado: boolean;
  finalizadoEm: string;
  url_documento: string;
  assinaturas: string;
  notificacoes: Notification[];
}

export interface DocumentUser {
  cnpj: string;
  nome: string;
}

export interface DocumentHistory {
  id: number;
  documento_id: number;
  signatario_id: number | null;
  interessado_id: number | null;
  descricao: string;
  categoria: string;
  created_at: string;
  titulo_id: number | null;
  uid_envio_sms: string | null;
}

export interface Signatory {
  id: number;
  nome: string;
  cpfCnpj: string;
  email: string;
  dataNasc: string | null;
  celular: string | null;
  pivot: SignatoryPivot;
  url_selfie: string | null;
  latitude: number;
  longitude: number;
  ordemAssinatura: number;
  serproBiometriaFacial: SerproFacialBiometry;
  faceMatch: FaceMatch;
  grupoAssinatura: SignatureGroup | null;
  files: SignatoryFile[];
}

export interface SignatoryPivot {
  documento_id: number;
  status: string;
  tipo: string;
  assinado_em: string;
  mensagem: string;
  ultimo_envio_token_email: string | null;
  ultimo_envio_token_sms: string | null;
  tipo_notificacao: string;
  grupo_assinatura_id: number | null;
  ordem_assinatura: number;
  documentoSignatarioId: number;
  tipoComplemento: string | null;
  assinaturaNecessaria: string;
  certificadoDigitalNecessario: string;
  selfieNecessaria: string;
  documentoFrenteNecessario: string;
  documentoVersoNecessario: string;
  selfieComDocumentoNecessario: string;
  serproRealizarValidacaoFacial: string;
  faceMatch: string;
}

export interface SerproFacialBiometry {
  serproCpfDisponibilidade: string | null;
  serproDataNascimentoCorreta: string | null;
  serproFaceDisponibilidade: string | null;
  serproFaceProbabilidade: string | null;
  serproFaceSimilaridade: string | null;
}

export interface FaceMatch {
  realizarFaceMatch: string;
  faceMatchUuid: string | null;
  faceMatchDisponibilidade: string | null;
  faceMatchSimilaridade: string | null;
}

export interface SignatureGroup {
  id: number;
  nome: string;
  ordem: number;
}

export interface SignatoryFile {
  id: number;
  documento_id: number;
  signatario_id: number;
  nome: string;
  categoria: string;
  path: string;
  size: number;
  hash: string;
  migrado_s3: number;
  ip: string;
  latitude: number;
  longitude: number;
  device_id: string | null;
  created_at: string;
  certificado: string | null;
  valido: string;
}

export interface InterestedParty {
  id: number;
  nome: string;
  email: string;
  cpfCnpj: string;
  celular: string | null;
}

export interface Notification {
  id: number;
  documento_id: number;
  signatario_id: number | null;
  interessado_id: number | null;
  descricao: string;
  categoria: string;
  created_at: string;
  titulo_id: number | null;
  uid_envio_sms: string | null;
}
