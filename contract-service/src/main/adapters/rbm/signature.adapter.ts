// TODO: Missing error handling

import type { HttpClient } from "@/application/interfaces/http-client";
import {
  type ISignatureAPI,
  type SignatureRequest,
  SignatureStatus,
} from "@/application/interfaces/signature-api";
import type { InvestmentContract } from "@/domain/entities/contracts";
import { type Either, left, right } from "@/domain/shared/either";
import env from "@/main/config/env";
import { AuthRbm } from "./auth.adapter";
import type { ISendDocumentApiResponse } from "./interfaces/send-document";
import type { Document, IDocumentApiResponse } from "./interfaces/get-document";
import type {
  IAddSignatoriesApiRequest,
  IAddSignatoriesApiResponse,
} from "./interfaces/signatories";
import { Company } from "@/domain/entities/parties";
import type { Investor } from "@/domain/entities/user";

export class SignatureAdapter implements ISignatureAPI {
  private authRbm = new AuthRbm();
  private documentsUrl: string;
  private signatoriesUrl: string;
  private getDocumentUrl: string;

  constructor(private readonly httpClient: HttpClient) {
    this.documentsUrl = `${env.API_ASSINATURA_URL}/documentos`;
    this.signatoriesUrl = `${env.API_ASSINATURA_URL}/documentos/signatarios`;
    this.getDocumentUrl = `${env.API_ASSINATURA_URL}/v2/documentos/infos`;
  }

  async send(
    contract: InvestmentContract,
    file: Buffer
  ): Promise<Either<Error, SignatureRequest>> {
    if (!this.canSendDocument()) {
      return left(new Error("Indisponível aos finais de semana"));
    }

    const tokenResult = await this.getAuthToken();
    if (tokenResult.isLeft()) return left(tokenResult.value);
    const token = tokenResult.value;

    const formData = this.buildFormData(
      file,
      contract.getInvestor().getParty().getCpf()
    );

    const uploadResult = await this.uploadDocument(formData, token);
    if (uploadResult.isLeft()) return left(uploadResult.value);
    const document = uploadResult.value;

    const investor = contract.getInvestor();
    const signatoriesResult = await this.registerSignatories(
      document.id,
      token,
      investor
    );
    if (signatoriesResult.isLeft()) return left(signatoriesResult.value);

    const signatureRequest = this.mapToSignatureRequest(document);
    return right(signatureRequest);
  }

  async getStatus(
    contract: InvestmentContract
  ): Promise<Either<Error, SignatureStatus>> {
    const tokenResult = await this.getAuthToken();
    if (tokenResult.isLeft()) return left(tokenResult.value);
    const token = tokenResult.value;

    const headers = {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    };

    const getDocument = `${
      this.getDocumentUrl
    }/${contract.getSignatureRequestId()}`;

    try {
      const response = await this.httpClient.get<IDocumentApiResponse>(
        getDocument,
        {
          headers,
        }
      );

      if (response.data.erro) {
        return left(
          new Error(response.data.message || "Erro ao buscar documento.")
        );
      }

      const document = response.data.payload?.documento;
      if (!document) {
        return left(new Error("Documento não encontrado na resposta."));
      }

      const investorHasSigned = document.signatarios.find((signatory) => {
        return (
          signatory.cpfCnpj === contract.getInvestor().getCpf() &&
          signatory.pivot.status === "assinado"
        );
      });

      if (document.status === "finalizado") {
        return right(SignatureStatus.SIGNED_BY_ALL);
      }

      if (document.status === "em processo" && investorHasSigned) {
        return right(SignatureStatus.SIGNED_BY_INVESTOR);
      }

      return right(SignatureStatus.AWAITING_SIGNATURE);
    } catch (error: any) {
      return left(new Error(error.message || "Erro ao buscar documento"));
    }
  }

  async deleteContract(externalId: string): Promise<Either<Error, Document>> {
    const tokenResult = await this.getAuthToken();
    if (tokenResult.isLeft()) return left(tokenResult.value);
    const token = tokenResult.value;

    const headers = {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    };

    const updateDocument = `${this.documentsUrl}/${externalId}`;

    try {
      const data = {
        status: "cancelado",
      };

      const response = await this.httpClient.put<
        { status: string },
        IDocumentApiResponse
      >(updateDocument, data, {
        headers,
      });

      if (
        response.data.erro ||
        response.data?.payload?.documento?.status !== "cancelado"
      ) {
        return left(
          new Error(response.data.message || "Erro ao cancelar documento")
        );
      }

      return right(response.data.payload.documento);
    } catch (error: any) {
      return left(new Error(error.message || "Erro ao cancelar documento"));
    }
  }

  async documentExist(id: string): Promise<Either<Error, boolean>> {
    const tokenResult = await this.getAuthToken();
    if (tokenResult.isLeft()) return left(tokenResult.value);
    const token = tokenResult.value;

    const headers = {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    };

    const url = `${this.getDocumentUrl}/${id}`;

    try {
      const response = await this.httpClient.get<IDocumentApiResponse>(url, {
        headers,
      });
      if (!response.data.erro && !!response.data.payload) return right(true);

      return right(false);
    } catch (error: any) {
      return left(new Error(error.message || "Erro ao verificar documento"));
    }
  }

  async deleteContract(externalId: string): Promise<Either<Error, Document>> {
    const tokenResult = await this.getAuthToken();
    if (tokenResult.isLeft()) return left(tokenResult.value);
    const token = tokenResult.value;

    const headers = {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    };

    const updateDocument = `${this.documentsUrl}/${externalId}`;

    try {
      const data = {
        status: "cancelado",
      };

      const response = await this.httpClient.put<
        { status: string },
        IDocumentApiResponse
      >(updateDocument, data, {
        headers,
      });

      if (
        response.data.erro ||
        response.data?.payload?.documento?.status !== "cancelado"
      ) {
        return left(
          new Error(response.data.message || "Erro ao cancelar documento")
        );
      }

      return right(response.data.payload.documento);
    } catch (error: any) {
      return left(new Error(error.message || "Erro ao cancelar documento"));
    }
  }
  // ------------------- Métodos Privados -------------------

  private canSendDocument(): boolean {
    const currentDay = new Date().getDay();
    return !(
      env.NODE_ENV === "development" &&
      (currentDay === 6 || currentDay === 0)
    );
  }

  private async getAuthToken(): Promise<Either<Error, string>> {
    const tokenResult = await this.authRbm.authenticateIntegration(
      this.httpClient
    );
    if (tokenResult.isLeft()) return left(tokenResult.value);
    return right(tokenResult.value.token);
  }

  private buildFormData(file: Buffer, document: string): FormData {
    const formattedFile = new File([file], `${document}.pdf`, {
      type: "application/pdf",
    });

    // Cria o FormData e anexa o arquivo
    const formData = new FormData();
    formData.append("arquivo", formattedFile);
    return formData;
  }

  private async uploadDocument(
    formData: FormData,
    token: string
  ): Promise<Either<Error, { id: number }>> {
    const headers = {
      Authorization: `Bearer ${token}`,
      Accept: "application/json",
    };

    try {
      const response = await this.httpClient.post<
        FormData,
        ISendDocumentApiResponse
      >(this.documentsUrl, formData, { headers });

      if (response.data.error) {
        return left(
          new Error(response.data.message || "Erro ao registrar documento.")
        );
      }

      const document = response.data.payload?.documento;
      if (!document || document.id === undefined) {
        return left(new Error("Documento não encontrado na resposta."));
      }

      return right(document);
    } catch (error: any) {
      return left(new Error(error.message || "Erro ao enviar documento"));
    }
  }

  private async registerSignatories(
    documentId: number,
    token: string,
    investor: Investor
  ): Promise<Either<Error, void>> {
    const headers = {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    };
    const signatoriesUrl = `${this.signatoriesUrl}/${documentId}`;

    const party = investor.getParty();

    const signatoriesData: IAddSignatoriesApiRequest = {
      mensagem: "",
      signatarios: [
        {
          cpfCnpj: "***********",
          nome: "ICA BANK SOLUCOES FINANCEIRAS LTDA",
          tipoAssinatura: "administrador",
          tipoAutenticacao: "email",
          email: "<EMAIL>",
          selfieNecessaria: env.NODE_ENV === "production" ? "S" : "N",
          documentoFrenteNecessario: env.NODE_ENV === "production" ? "S" : "N",
          documentoVersoNecessario: env.NODE_ENV === "production" ? "S" : "N",
        },
        {
          cpfCnpj: investor.getCpf(),
          nome:
            party instanceof Company
              ? party.getLegalRepresentative().getName()
              : investor.getName(),
          tipoAssinatura: "investor",
          tipoAutenticacao: "email",
          email: investor.getEmail(),
          selfieNecessaria: env.NODE_ENV === "production" ? "S" : "N",
          documentoFrenteNecessario: env.NODE_ENV === "production" ? "S" : "N",
          documentoVersoNecessario: env.NODE_ENV === "production" ? "S" : "N",
        },
      ],
    };

    try {
      await this.httpClient.post<
        IAddSignatoriesApiRequest,
        IAddSignatoriesApiResponse
      >(signatoriesUrl, signatoriesData, { headers });

      return right(undefined);
    } catch (error: any) {
      return left(new Error(error.message || "Erro ao registrar signatários"));
    }
  }

  private mapToSignatureRequest(document: { id: number }): SignatureRequest {
    return {
      requestId: document.id.toString(),
    };
  }
}
