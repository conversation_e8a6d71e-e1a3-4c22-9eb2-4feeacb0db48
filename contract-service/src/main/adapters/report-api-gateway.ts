import type { HttpClient } from '@/application/interfaces/http-client'
import type {
  IReportGateway,
  IncomeReportPayload,
} from '@/domain/gateways/report.gateway'
import { type Either, left, right } from '@/domain/shared/either'
import env from '../config/env'

interface BankAccount {
  agency: string
  account: string
}

interface Employee {
  name: string
  document: string
  bankAccount: BankAccount
}

interface NetIncome {
  name: string
  amount: number
}

interface IncomeData {
  calendarYear: string
  companyName: string
  companyDocument: string
  employee: Employee
  applicationBalance: number
  netIncome: NetIncome[]
}

export interface IDataReport {
  type: string
  data: IncomeData
}

export class ReportApiGateway implements IReportGateway {
  private readonly apiBaseUrl: string

  constructor(private readonly httpClient: HttpClient) {
    this.apiBaseUrl = env.REPORT_API_URL
  }

  async generateIncomeReport(
    data: IncomeReportPayload
  ): Promise<Either<Error, any>> {
    const reportData: IDataReport = {
      type: 'income',
      data: {
        applicationBalance: data.applicationBalance,
        calendarYear: data.calendarYear,
        companyName: 'ICA SOLUCOES FINANCEIRAS SA',
        companyDocument: '**************',
        employee: {
          name: data.employee.name,
          document: data.employee.document,
          bankAccount: {
            agency: data.employee.bankAccount.agency,
            account: data.employee.bankAccount.account,
          },
        },
        netIncome: [
          {
            name: 'Renda Fixa',
            amount: data.amount,
          },
        ],
      },
    }

    const response = await this.httpClient.post<IDataReport, Buffer>(
      `${this.apiBaseUrl}/generate_pdf?download=1`,
      reportData,
      {
        headers: {
          'Content-Type': 'application/json',
        },
        responseType: 'arraybuffer',
      }
    )

    if (response.statusCode !== 200) {
      return left(new Error('Erro ao enviar arquivo para api'))
    }

    return right(response.data)
  }
}
