// investment-contract.api.mapper.ts

import type { InvestmentContract } from '@/domain/entities/contracts'
import { type Company, Individual } from '@/domain/entities/parties'
import { QuotaPolicy } from '@/domain/services/quota-policy'
import type {
  Company as APICompany,
  APIContractPayload,
  Individual as APIIndividual,
  BankAccount,
  ContractType,
  InvestmentMutuo,
  InvestmentSCP,
  PFContractMutuo,
  PFContractSCP,
  PJContractMutuo,
  PJContractSCP,
  PersonType,
} from './interfaces/request'

/**
 * Mapper que converte uma instância de InvestmentContract do domínio para
 * o payload esperado pela API externa.
 */
export class InvestmentContractApiMapper {
  /**
   * Converte o InvestmentContract para o payload da API.
   */

  private static readonly profileMap = {
    conservative: 'CONSERVADOR',
    aggressive: 'AGRESSIVO',
    moderate: 'MODERADO',
  } as const

  private static mapProfile(
    profile: 'conservative' | 'moderate' | 'aggressive'
  ): 'CONSERVADOR' | 'AGRESSIVO' | 'MODERADO' {
    return InvestmentContractApiMapper.profileMap[profile] ?? 'CONSERVADOR'
  }
  public static toApi(contract: InvestmentContract): APIContractPayload {
    const isMutuo: boolean = contract.getContractType().isMutuo()
    const domainContractType: ContractType = contract
      .getContractType()
      .isMutuo()
      ? 'MUTUO'
      : 'SCP' // 'MUTUO' ou 'SCP'
    const investor = contract.getInvestor()
    const party = investor.getParty()

    // Determina o personType com base na existência de método getCpf (PF) ou getCnpj (PJ)
    const personType: PersonType = party instanceof Individual ? 'PF' : 'PJ'

    // Calcula a duração do contrato (em meses)
    const durationInMonths: number = this.calculateDurationInMonths(
      contract.getStartDate(),
      contract.getEndDate()
    )

    // Mapeamento dos dados do investimento conforme o tipo de contrato
    let investmentMutuo: InvestmentMutuo | undefined
    let investmentSCP: InvestmentSCP | undefined


    const profile = contract.getProfile().value
    const quotaQuantity = contract.getQuotaQuantity()


    

    if (isMutuo) {
      investmentMutuo = {
        amount: contract.getAmount().amount,
        monthlyRate: contract.getProfitability().valueAsPercent,
        durationInMonths: durationInMonths,
        paymentMethod: contract.getPaymentMethod().value,
        endDate: this.formatDate(contract.getEndDate()),
        profile: this.mapProfile(profile),
        isDebenture: false,
        startDate: this.formatDate(contract.getStartDate()),
      }
    } else {
      investmentSCP = {
        quota: quotaQuantity,
        monthlyRate: contract.getProfitability().valueAsPercent,
        durationInMonths: durationInMonths,
      }
    }

    // Mapeamento da conta bancária do investidor
    const bankAccount: BankAccount = {
      bank: investor.getAccount().bank,
      agency: investor.getAccount().agency,
      account: investor.getAccount().accountNumber,
    }
    const contractNumber = contract.getContractNumber()?.toString() ?? ''

    // Mapeamento dos dados da parte (pessoa física ou jurídica)
    if (personType === 'PF') {
      const individual: APIIndividual = this.mapIndividual(party as Individual)
      if (domainContractType === 'MUTUO') {
        const payload: PFContractMutuo = {
          brokerId: contract.getBroker(),
          contractNumber,
          personType: 'PF',
          contractType: 'MUTUO',
          individual,
          investment: investmentMutuo as InvestmentMutuo,
          bankAccount,
        }
        return payload
      }
      const payload: PFContractSCP = {
        brokerId: contract.getBroker(),
        contractNumber,
        personType: 'PF',
        contractType: 'SCP',
        individual,
        investment: investmentSCP as InvestmentSCP,
        bankAccount,
      }
      return payload
    }

    const company: APICompany = this.mapCompany(party as Company)
    if (domainContractType === 'MUTUO') {
      const payload: PJContractMutuo = {
        brokerId: contract.getBroker(),
        contractNumber,
        personType: 'PJ',
        contractType: 'MUTUO',
        company,
        investment: investmentMutuo as InvestmentMutuo,
        bankAccount,
      }
      return payload
    }

    const payload: PJContractSCP = {
      brokerId: contract.getBroker(),
      contractNumber,
      personType: 'PJ',
      contractType: 'SCP',
      company,
      investment: investmentSCP as InvestmentSCP,
      bankAccount,
    }
    return payload
  }

  /**
   * Calcula a duração em meses entre duas datas.
   */
  private static calculateDurationInMonths(start: Date, end: Date): number {
    const years = end.getFullYear() - start.getFullYear()
    const months = end.getMonth() - start.getMonth()
    return years * 12 + months
  }

  /**
   * Formata uma data no padrão 'YYYY-MM-DD'.
   */
  private static formatDate(date: Date): string {
    return date.toISOString().split('T')[0]
  }

  /**
   * Mapeia um DomainIndividual para o tipo Individual (payload da API).
   */
  private static mapIndividual(individual: Individual): APIIndividual {
    return {
      fullName: individual.getName(),
      cpf: individual.getCpf(),
      rg: individual.getRg() ?? '',
      issuingAgency: individual.getIssuingAgency() ?? 'SSP',
      nationality: individual.getNationality() ?? 'brasileira',
      occupation: individual.getOccupation() ?? '',
      birthDate: this.formatDate(individual.getBirthDate()),
      email: individual.getEmail(),
      phone: individual.getPhone(),
      motherName: individual.getMotherName(),
      address: individual.getAddress(),
    }
  }

  /**
   * Mapeia um DomainCompany para o tipo Company (payload da API).
   */
  private static mapCompany(company: Company): APICompany {
    return {
      cnpj: company.getCnpj(),
      corporateName: company.getName(),
      type: company.getCompanyType().getValue(),
      address: company.getAddress(),
      representative: this.mapIndividual(company.getLegalRepresentative()),
    }
  }
}
