import type { HttpClient, IContractGenerator } from '@/application/interfaces'
import type { InvestmentContract } from '@/domain/entities/contracts'
import type { LoggerGateway } from '@/domain/gateways/logger.gateway'
import { type Either, left, right } from '@/domain/shared'
import { FetchHttpClient } from '@/infrastructure/http/fetch-http-client'
import env from '@/main/config/env'
import { PinoLoggerAdapter } from '../pino-logger.adapter'
import { InvestmentContractApiMapper } from './contract-generator.mapper'
import type { APIContractPayload } from './interfaces/request'

export class ContractGeneratorAdapter implements IContractGenerator {
  private readonly apiBaseUrl: string
  private readonly httpClient: HttpClient
  private readonly logger: LoggerGateway

  constructor() {
    this.apiBaseUrl = env.CONTRACT_API_URL
    this.httpClient = new FetchHttpClient()
    this.logger = new PinoLoggerAdapter()
  }

  async generate(contract: InvestmentContract): Promise<Either<Error, Buffer>> {
    this.logger.info(
      `Iniciando geração do contrato PDF para o contrato de ID: ${contract.id}`
    )

    const payload = InvestmentContractApiMapper.toApi(contract)

    

    this.logger.info(`Payload gerado: ${JSON.stringify(payload, null, 2)}`)

    const response = await this.httpClient.post<APIContractPayload, any>(
      `${this.apiBaseUrl}/v2/generate_pdf?download=1`,
      payload,
      {
        headers: {
          'Content-Type': 'application/json',
        },
        responseType: 'arraybuffer',
      }
    )

    if (response.statusCode > 299) {
      return left(new Error(`Erro ao gerar arquivo: ${response.data}`))
    }

    this.logger.info(
      `Geração do contrato PDF concluída com sucesso para o contrato de ID: ${contract.id}`
    )

    return right(response.data)
  }
}
