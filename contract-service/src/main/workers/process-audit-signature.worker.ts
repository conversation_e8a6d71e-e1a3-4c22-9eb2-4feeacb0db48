import type { LoggerGateway } from '@/domain/gateways/logger.gateway';
import { QueueNames } from '../config/queue-names';
import { createWorkerAdapter } from '../factories/bullmq';
import { ContractStatus } from '@/domain/entities/contracts';
import { makeProcessAuditSignatureUseCase } from '../factories/usecases/process-audit-signature.factory';

export const setupProcessAuditSignatureWorker = async (
  logger: LoggerGateway
) => {
  const worker = createWorkerAdapter(QueueNames.PROCESS_AUDIT_SIGNATURE);
  const processAuditSignatureUseCase = makeProcessAuditSignatureUseCase();

  await worker.process<{ id: string; status: ContractStatus }, any>(
    QueueNames.PROCESS_AUDIT_SIGNATURE,
    processAuditSignatureUseCase.execute.bind(processAuditSignatureUseCase)
  );

  logger.info('Audit Signature worker initialized');

  return worker;
};
