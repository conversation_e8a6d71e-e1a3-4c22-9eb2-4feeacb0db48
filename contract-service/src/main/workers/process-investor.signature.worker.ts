import type { LoggerGateway } from '@/domain/gateways/logger.gateway';
import { QueueNames } from '../config/queue-names';
import { createWorkerAdapter } from '../factories/bullmq';
import { makeProcessInvestorSignatureUseCase } from '../factories/usecases/process-investor-signature.factory';
import { ContractStatus } from '@/domain/entities/contracts';

export const setupProcessInvestorSignatureWorker = async (
  logger: LoggerGateway
) => {
  const worker = createWorkerAdapter(QueueNames.PROCESS_INVESTOR_SIGNATURE);
  const processInvestorSignatureUseCase = makeProcessInvestorSignatureUseCase();

  await worker.process<{ id: string; status: ContractStatus }, any>(
    QueueNames.PROCESS_INVESTOR_SIGNATURE,
    processInvestorSignatureUseCase.execute.bind(
      processInvestorSignatureUseCase
    )
  );

  logger.info('Investor Signature worker initialized');

  return worker;
};
