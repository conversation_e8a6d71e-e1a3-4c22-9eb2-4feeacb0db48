import type { IncomeReportQueueJobData } from '@/application/usecases';
import type { LoggerGateway } from '@/domain/gateways/logger.gateway';
import { QueueNames } from '../config/queue-names';
import { createWorkerAdapter } from '../factories/bullmq';
import { makeProcessIncomeReportUseCase } from '../factories/usecases/process-income-report-factory';

export const setupIncomeReportWorker = async (logger: LoggerGateway) => {
  const worker = createWorkerAdapter(QueueNames.INVESTOR_REPORT);
  const processIncomeReportUseCase = makeProcessIncomeReportUseCase();

  await worker.process<IncomeReportQueueJobData, any>(
    QueueNames.INVESTOR_REPORT,
    processIncomeReportUseCase.execute.bind(processIncomeReportUseCase),
  );

  logger.info('Income report worker initialized');

  return worker;
};
