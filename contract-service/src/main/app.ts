import Fastify from "fastify";
import { contractRoutes } from "./routes/contracts/contract.route";
import { createExistingContractRoute } from "./routes/contracts/create-existing-contract.route";
import { createNewContractRoute } from "./routes/contracts/create-new-contract.route";
import { resubmitRejectedContractRoute } from "./routes/contracts/resubmit-rejected-contract.route";
import { detailsIncomeReportRoute } from "./routes/details-income-report.route";
import { healthRoutes } from "./routes/health-check.route";
import { incomeReportRoute } from "./routes/request-income-report.route";
import { webhookEmailRoute } from "./routes/webhook-email.route";
import { deleteContractRoute } from "./routes/contracts/delete-contract.route";
import { editContractRoute } from "./routes/contracts/edit-contract.route";

export const app = Fastify({ logger: true });

export const setupRoutes = () => {
  app.register(healthRoutes);
  app.register(incomeReportRoute);
  app.register(contractRoutes);
  app.register(createNewContractRoute);
  app.register(detailsIncomeReportRoute);
  app.register(webhookEmailRoute);
  app.register(createExistingContractRoute);
  app.register(resubmitRejectedContractRoute);
  app.register(deleteContractRoute);
  app.register(editContractRoute);
};
