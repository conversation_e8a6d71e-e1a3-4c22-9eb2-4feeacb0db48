import { ResubmitRejectedContractController } from '@/presentation/http/controllers/contracts/resubmit-rejected-contract.controller';
import { makeResubmitRejectedContractUseCase } from '../../usecases/resubmit-rejected-contract.factory';
import { ResubmitRejectedContractValidator } from '@/main/validators/zod/resubmit-rejected-contract.validator';

export const makeResubmitRejectedContractController =
  (): ResubmitRejectedContractController => {
    const useCase = makeResubmitRejectedContractUseCase();
    const validation = new ResubmitRejectedContractValidator();

    return new ResubmitRejectedContractController(validation, useCase);
  };
