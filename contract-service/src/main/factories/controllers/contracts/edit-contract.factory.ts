import { EditContractValidator } from "@/main/validators/zod/edit-contract.validator";
import { makeEditContractUseCase } from "../../usecases/edit-contract.factory";
import { EditContractController } from "@/presentation/http/controllers/contracts/edit-contract.controller";

export const makeEditContractController = (): EditContractController => {
  const useCase = makeEditContractUseCase();
  const validation = new EditContractValidator();

  return new EditContractController(validation, useCase);
};
