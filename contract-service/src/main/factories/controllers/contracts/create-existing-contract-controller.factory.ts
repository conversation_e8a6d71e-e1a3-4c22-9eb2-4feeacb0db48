import { CreateExistingContractValidator } from '@/main/validators/zod/create-existing-contract.validator'
import { CreateExistingContractController } from '@/presentation/http/controllers/contracts/create-existing-contract.controller'
import { makeCreateExistingContractUseCase } from '../../usecases/create-existing-contract.factory'

export function makeCreateExistingContractController() {
  const useCase = makeCreateExistingContractUseCase()
  const validator = new CreateExistingContractValidator()
  const controller = new CreateExistingContractController(useCase, validator)
  return controller
}
