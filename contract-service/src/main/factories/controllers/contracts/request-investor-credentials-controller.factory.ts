import { RequestInvestorCredentialsController } from '@/presentation/http/controllers/contracts/create-investor-credentials.controller'
import type { IController } from '@/presentation/http/protocols'
import { makeRequestInvestorCredentialsUseCase } from '../../usecases/request-investor-credentials.factory'

export const makeRequestInvestorCredentialsController = (): IController => {
  const useCase = makeRequestInvestorCredentialsUseCase()
  return new RequestInvestorCredentialsController(useCase)
}
