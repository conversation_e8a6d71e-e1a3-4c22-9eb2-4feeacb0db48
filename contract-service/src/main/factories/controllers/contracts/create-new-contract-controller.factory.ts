import { CreateContractValidator } from '@/main/validators/zod/create-new-contract.validator'
import { CreateNewContractController } from '@/presentation/http/controllers/contracts/create-new-contract.controller'
import { makeCreateNewContractUseCase } from '../../usecases/create-new-contract.factory'

export function makeCreateNewContractController() {
  const useCase = makeCreateNewContractUseCase()
  const validator = new CreateContractValidator()
  const controller = new CreateNewContractController(useCase, validator)

  return controller
}
