import { ListContractsUseCase } from '@/application/usecases/contracts/list-contracts.usecase'
import { PrismaInvestmentContractRepository } from '@/infrastructure/prisma/repositories'

export function makeListContractsUseCase() {
  const prismaInvestmentContractRepository =
    new PrismaInvestmentContractRepository()

  const useCase = new ListContractsUseCase(prismaInvestmentContractRepository)

  return useCase
}
