import { GetIncomeReportDetailsUseCase } from '@/contexts/income-report/application/usecases/get-income-report-details.usecase';
import { PrismaContractRepository } from '@/contexts/income-report/infrastructure/prisma/repositories/contract.repository';
import { PrismaInvestorRepository } from '@/contexts/income-report/infrastructure/prisma/repositories/investor.repository';

export function makeDetailsIncomeReportUseCase() {
  const prismaContractRepository = new PrismaContractRepository();
  const prismaInvestorRepository = new PrismaInvestorRepository();

  const useCase = new GetIncomeReportDetailsUseCase(
    prismaContractRepository,
    prismaInvestorRepository,
  );

  return useCase;
}
