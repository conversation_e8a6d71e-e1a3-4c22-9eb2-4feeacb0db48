import { CreateNewContractUseCase } from "@/application/usecases/contracts/create-new-contract.usecase";
import prisma from "@/infrastructure/prisma/client";
import { PrismaUnitOfWork } from "@/infrastructure/prisma/prisma-unit-of-work";
import { PrismaInvestmentContractRepository } from "@/infrastructure/prisma/repositories";
import { PrismaAdvisorRepository } from "@/infrastructure/prisma/repositories/advisor.repository";
import { PrismaBrokerRepository } from "@/infrastructure/prisma/repositories/broker.repository";
import { PrismaInvestorRepository } from "@/infrastructure/prisma/repositories/investor.repository";
import { PinoLoggerAdapter } from "@/main/adapters/pino-logger.adapter";

export function makeCreateNewContractUseCase() {
  const unitOfWork = new PrismaUnitOfWork(prisma);

  const prismaInvestmentContractRepository =
    new PrismaInvestmentContractRepository();

  const prismaAdvisorRepository = new PrismaAdvisorRepository();

  const prismaInvestorRepository = new PrismaInvestorRepository();

  const prismaBrokerRepository = new PrismaBrokerRepository();

  const pinoLogger = new PinoLoggerAdapter();

  const useCase = new CreateNewContractUseCase(
    unitOfWork,
    prismaInvestmentContractRepository,
    prismaAdvisorRepository,
    prismaInvestorRepository,
    prismaBrokerRepository,
    pinoLogger
  );

  return useCase;
}
