import { ProcessAuditSignatureUseCase } from '@/application/usecases/process-audit-signature/process-audit-signature.usecase';
import { FetchHttpClient } from '@/infrastructure/http/fetch-http-client';
import { PrismaInvestmentContractRepository } from '@/infrastructure/prisma/repositories';
import { PinoLoggerAdapter } from '@/main/adapters/pino-logger.adapter';

import { SignatureAdapter } from '@/main/adapters/rbm/signature.adapter';

export function makeProcessAuditSignatureUseCase() {
  const fetchHttp = new FetchHttpClient();
  const prismaInvestmentContractRepository =
    new PrismaInvestmentContractRepository();
  const signatureApi = new SignatureAdapter(fetchHttp);
  const pinoLoggerAdapter = new PinoLoggerAdapter();

  const useCase = new ProcessAuditSignatureUseCase(
    prismaInvestmentContractRepository,
    signatureApi,
    pinoLoggerAdapter
  );

  return useCase;
}
