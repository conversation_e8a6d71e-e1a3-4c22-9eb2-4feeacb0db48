import { CreateExistingContractUseCase } from '@/application/usecases/contracts/create-existing-contract.usecase'
import prisma from '@/infrastructure/prisma/client'
import { PrismaUnitOfWork } from '@/infrastructure/prisma/prisma-unit-of-work'
import { PrismaInvestmentContractRepository } from '@/infrastructure/prisma/repositories'
import { PrismaAdvisorRepository } from '@/infrastructure/prisma/repositories/advisor.repository'
import { PrismaBrokerRepository } from '@/infrastructure/prisma/repositories/broker.repository'
import { PrismaInvestorRepository } from '@/infrastructure/prisma/repositories/investor.repository'
import { PinoLoggerAdapter } from '@/main/adapters/pino-logger.adapter'

export function makeCreateExistingContractUseCase() {
  const unitOfWork = new PrismaUnitOfWork(prisma)
  const contractRepository = new PrismaInvestmentContractRepository()
  const advisorRepository = new PrismaAdvisorRepository()
  const investorRepository = new PrismaInvestorRepository()
  const brokerRepository = new PrismaBrokerRepository()
  const logger = new PinoLoggerAdapter()

  return new CreateExistingContractUseCase(
    unitOfWork,
    contractRepository,
    advisorRepository,
    investorRepository,
    brokerRepository,
    logger
  )
}
