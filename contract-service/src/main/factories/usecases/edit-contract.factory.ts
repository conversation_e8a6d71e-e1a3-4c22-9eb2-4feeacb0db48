import { EditContractUseCase } from "@/application/usecases/contracts/edit-contract.usecase";
import { PrismaUnitOfWork } from "@/infrastructure/prisma/prisma-unit-of-work";
import { PinoLoggerAdapter } from "@/main/adapters/pino-logger.adapter";
import prisma from "@/infrastructure/prisma/client";
import { PrismaInvestmentContractRepository } from "@/infrastructure/prisma/repositories";
import { FetchHttpClient } from "@/infrastructure/http/fetch-http-client";
import { SignatureAdapter } from "@/main/adapters/rbm/signature.adapter";

export const makeEditContractUseCase = (): EditContractUseCase => {
  const unitOfWork = new PrismaUnitOfWork(prisma);
  const contractRepository = new PrismaInvestmentContractRepository();
  const logger = new PinoLoggerAdapter();
  const fetchHttpClient = new FetchHttpClient();
  const rmbApi = new SignatureAdapter(fetchHttpClient);

  return new EditContractUseCase(
    unitOfWork,
    contractRepository,
    rmbApi,
    logger
  );
};
