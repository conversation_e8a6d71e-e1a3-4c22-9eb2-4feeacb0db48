import prisma from "@/infrastructure/prisma/client";
import { PrismaUnitOfWork } from "@/infrastructure/prisma/prisma-unit-of-work";
import { PrismaInvestmentContractRepository } from "@/infrastructure/prisma/repositories";
import { PinoLoggerAdapter } from "@/main/adapters/pino-logger.adapter";
import { AwsStorageGateway } from "@/main/adapters/aws-storage.adapter";
import env from "@/main/config/env";
import { ResubmitRejectedContractUseCase } from "@/application/usecases/contracts/resubmit-rejected-contract.usecase";

export function makeResubmitRejectedContractUseCase() {
  const unitOfWork = new PrismaUnitOfWork(prisma);
  const contractRepository = new PrismaInvestmentContractRepository();
  const logger = new PinoLoggerAdapter();
  const storage = new AwsStorageGateway(env.AWS_BUCKET_NAME, env.AWS_REGION);

  return new ResubmitRejectedContractUseCase(
    unitOfWork,
    contractRepository,
    logger,
    storage
  );
}
