import { RequestInvestorCredentialsUseCase } from '@/application/usecases/contracts/request-investor-credentials.usecase'
import { PrismaInvestmentContractRepository } from '@/infrastructure/prisma/repositories'

export const makeRequestInvestorCredentialsUseCase =
  (): RequestInvestorCredentialsUseCase => {
    const contractRepository = new PrismaInvestmentContractRepository()

    return new RequestInvestorCredentialsUseCase(contractRepository)
  }
