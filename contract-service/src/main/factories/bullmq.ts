import { redisConnection } from '@/infrastructure/redis/client'
import { BullQ<PERSON>ueAdapter, BullWorkerAdapter } from '../adapters'

export const createQueueAdapter = (queueName: string) => {
  return new BullQueueAdapter(queueName, { connection: redisConnection })
}

export const createWorkerAdapter = (queueName: string, concurrency = 10) => {
  return new BullWorkerAdapter(queueName, {
    connection: redisConnection,
    concurrency,
  })
}
