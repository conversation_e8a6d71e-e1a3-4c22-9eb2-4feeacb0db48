import { PrismaInvestmentContractRepository } from '@/infrastructure/prisma/repositories';
import { QueueNames } from '@/main/config/queue-names';
import { createQueueAdapter } from '../bullmq';
import { AwaitingInvestorSignature } from '@/infrastructure/jobs/node-cron/awaiting-investor-signature.job';

export function makeProcessInvestorSignatureJob() {
  const investorRepository = new PrismaInvestmentContractRepository();

  const queueGateway = createQueueAdapter(
    QueueNames.PROCESS_INVESTOR_SIGNATURE
  );

  const awaitingInvestorSignature = new AwaitingInvestorSignature(
    investorRepository,
    queueGateway
  );

  return awaitingInvestorSignature;
}
