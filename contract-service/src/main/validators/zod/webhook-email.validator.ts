import { z } from 'zod';

import { type Either, left, right } from '@/domain/shared/either';
import {
  type IValidator,
  ValidationError,
} from '@/presentation/http/validation/validator';
import { WebhookEmailDto } from '@/contexts/income-report/application/dto/webhook-email.dto';

export const WebhookEmailSchema = z.object({
  created_at: z.string(),
  data: z.object({
    status: z.enum([
      'PENDING',
      'DELIVERING',
      'SENT',
      'DELAYED',
      'BOUNCED',
      'COMPLAINED',
      'RETRY',
      'ERRORED',
    ]), // Status permitidos
    email_id: z
      .string()
      .uuid({ message: 'ID do e-mail deve ser um UUID válido' }),
  }),
});

export class WebhookEmailValidator implements IValidator<WebhookEmailDto> {
  private schema = WebhookEmailSchema;

  validate(input: unknown): Either<ValidationError, WebhookEmailDto> {
    const result = this.schema.safeParse(input);

    if (!result.success) {
      const messages = result.error.errors.map((issue) => issue.message);
      return left(new ValidationError(messages));
    }

    return right(result.data);
  }
}
