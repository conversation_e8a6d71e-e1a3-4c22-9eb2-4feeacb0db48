import type { CreateExistingContractDTO } from '@/application/dtos/contracts/create-existing-contract.dto'
import {
  CompanyLegalType,
  InvestorProfile,
  PaymentMethod,
} from '@/application/dtos/contracts/create-new-contract.dto'
import { type Either, left, right } from '@/domain/shared/either'
import { ValidationError } from '@/presentation/http/validation/validator'
import type { IValidator } from '@/presentation/http/validation/validator'
import { z } from 'zod'
import { BankAccountSchema } from './create-new-contract.validator'

export enum PersonType {
  PF = 'PF',
  PJ = 'PJ',
}

export enum ContractType {
  MUTUO = 'MUTUO',
  SCP = 'SCP',
}

const FileSchema = z.object({
  mimetype: z.union(
    [
      z.literal('application/pdf'),
      z.literal('image/png'),
      z.literal('image/jpeg'),
      z.literal('image/jpg'),
    ],
    {
      errorMap: () => ({
        message:
          'O arquivo deve estar no formato PDF ou imagem (PNG, JPEG, JPG)',
      }),
    }
  ),
  buffer: z.instanceof(<PERSON><PERSON><PERSON>, {
    message: 'Conteúdo do arquivo inválido',
  }),
})

const AddressSchema = z.object({
  street: z.string({ required_error: 'Rua é obrigatória' }),
  number: z.string({ required_error: 'Número é obrigatório' }),
  city: z.string({ required_error: 'Cidade é obrigatória' }),
  state: z.string({ required_error: 'Estado é obrigatório' }),
  postalCode: z.string({ required_error: 'CEP é obrigatório' }),
  neighborhood: z.string({ required_error: 'Bairro é obrigatório' }),
  complement: z.string().optional(),
})

export const AdvisorAssignmentSchema = z.object({
  advisorId: z.string({ required_error: 'ID do assessor é obrigatório' }),
  rate: z
    .string()
    .transform(val => Number(val))
    .pipe(
      z
        .number()
        .min(0, 'Porcentagem deve ser maior ou igual a zero')
        .max(100, 'Porcentagem deve ser menor ou igual a 100')
    ),
})

export const IndividualSchema = z.object({
  fullName: z.string({ required_error: 'Nome completo é obrigatório' }),
  cpf: z.string({ required_error: 'CPF é obrigatório' }),
  rg: z.string({ required_error: 'RG é obrigatório' }),
  issuingAgency: z.string({ required_error: 'Órgão emissor é obrigatório' }),
  nationality: z.string({ required_error: 'Nacionalidade é obrigatória' }),
  occupation: z.string({ required_error: 'Ocupação é obrigatória' }),
  birthDate: z.string({ required_error: 'Data de nascimento é obrigatória' }),
  email: z
    .string({ required_error: 'Email é obrigatório' })
    .email('Email inválido'),
  phone: z.string({ required_error: 'Telefone é obrigatório' }),
  motherName: z.string({ required_error: 'Nome da mãe é obrigatório' }),
  address: AddressSchema,
})

export const CompanySchema = z.object({
  corporateName: z.string({ required_error: 'Razão social é obrigatória' }),
  cnpj: z.string({ required_error: 'CNPJ é obrigatório' }),
  type: z.nativeEnum(CompanyLegalType, {
    errorMap: () => ({ message: 'Tipo de empresa inválido' }),
  }),
  address: AddressSchema,
  representative: IndividualSchema,
})

const ExistingContractInvestmentSchema = z.object({
  amount: z
    .string()
    .transform(val => Number(val))
    .pipe(
      z
        .number({
          required_error: 'Valor do investimento é obrigatório',
          invalid_type_error: 'Valor do investimento deve ser um número',
        })
        .min(1, 'Valor do investimento deve ser maior que zero')
    ),
  monthlyRate: z
    .string()
    .transform(val => Number(val))
    .pipe(
      z
        .number({
          required_error: 'Taxa mensal é obrigatória',
          invalid_type_error: 'Taxa mensal deve ser um número',
        })
        .min(0, 'Taxa mensal deve ser maior ou igual a zero')
    ),
  paymentMethod: z.nativeEnum(PaymentMethod, {
    errorMap: () => ({
      message:
        'Método de pagamento inválido. Tipos válidos: pix, bank_transfer, boleto',
    }),
  }),
  profile: z.nativeEnum(InvestorProfile, {
    errorMap: () => ({
      message:
        'Tipo de pessoa inválido. Tipos válidos: conservative, moderate e aggressive',
    }),
  }),
  startDate: z
    .string({ required_error: 'Data de início é obrigatória' })
    .datetime({ message: 'Data de início inválida' }),
  endDate: z
    .string({ required_error: 'Data de término é obrigatória' })
    .datetime({ message: 'Data de término inválida' }),
  durationInMonths: z
    .string()
    .transform(val => Number(val))
    .pipe(
      z
        .number({
          required_error: 'Prazo em meses é obrigatório',
          invalid_type_error: 'O prazo deve ser um número',
        })
        .int('O prazo deve ser um número inteiro')
        .min(1, 'Prazo deve ser maior que zero')
    ),
  isDebenture: z
    .enum(['true', 'false'], {
      errorMap: () => ({ message: 'Debenture deve ser true ou false' }),
    })
    .transform(val => val === 'true')
    .pipe(
      z.boolean({
        required_error: 'Debenture é obrigatória',
        invalid_type_error: 'Debenture deve ser um booleano',
      })
    ),
    quotaQuantity: z
    .union([z.string(), z.number()])
    .transform(val => Number(val))
    .refine(val => !isNaN(val), { message: 'Quantidade de cotas inválida' })
    .optional(),
})

const CreateExistingContractSchema = z
  .object({
    personType: z.nativeEnum(PersonType, {
      errorMap: () => ({
        message: 'Tipo de pessoa inválido. Tipos válidos: PF e PJ',
      }),
    }),
    contractType: z.nativeEnum(ContractType, {
      errorMap: () => ({
        message: 'Tipo de contrato inválido. Tipos válidos: MUTUO ou SCP',
      }),
    }),
    brokerId: z.string({ message: 'Broker é obrigatório' }),
    investment: ExistingContractInvestmentSchema,
    advisors: z.array(AdvisorAssignmentSchema).default([]),
    bankAccount: BankAccountSchema,
    individual: IndividualSchema.optional(),
    company: CompanySchema.optional(),
    proofOfPayment: FileSchema,
    personalDocument: FileSchema,
    contract: FileSchema,
    proofOfResidence: FileSchema,
    companyDocument: FileSchema.optional(),
  })
  .refine(data => data.personType !== PersonType.PF || !!data.individual, {
    message: 'Os dados do investidor pessoa física são obrigatórios',
    path: ['individual'],
  })
  .refine(data => data.personType !== PersonType.PJ || !!data.company, {
    message: 'Os dados da empresa são obrigatórios para contratos PJ',
    path: ['company'],
  })
  .refine(
    data => {
      const startDate = new Date(data.investment.startDate)
      const endDate = new Date(data.investment.endDate)
      return endDate > startDate
    },
    {
      message: 'A data de término deve ser posterior à data de início',
      path: ['investment.endDate'],
    }
  )

export class CreateExistingContractValidator
  implements IValidator<CreateExistingContractDTO>
{
  validate(input: unknown): Either<ValidationError, CreateExistingContractDTO> {
    const result = CreateExistingContractSchema.safeParse(input)

    if (!result.success) {
      return left(
        new ValidationError(result.error.errors.map(error => error.message))
      )
    }

    const data = result.data
    const transformedData: CreateExistingContractDTO = {
      ...data,
      advisors: data.advisors.map(advisor => ({
        advisorId: advisor.advisorId,
        rate: advisor.rate,
      })),
      companyDocument: data.companyDocument && {
        mimetype: data.companyDocument?.mimetype,
        buffer: data.companyDocument?.buffer,
      },
      proofOfPayment: {
        mimetype: data.proofOfPayment.mimetype,
        buffer: data.proofOfPayment.buffer,
      },
      personalDocument: {
        mimetype: data.personalDocument.mimetype,
        buffer: data.personalDocument.buffer,
      },
      proofOfResidence: {
        mimetype: data.proofOfResidence.mimetype,
        buffer: data.proofOfResidence.buffer,
      },
      contract: {
        mimetype: data.contract.mimetype,
        buffer: data.contract.buffer,
      },
    }

    return right(transformedData)
  }
}
