import type { EditContractDTO } from "@/application/dtos/contracts/edit-contract.dto";
import {
  CompanyLegalType,
  PaymentMethod,
  InvestorProfile,
} from "@/application/dtos/contracts/create-new-contract.dto";
import { type Either, left, right } from "@/domain/shared/either";
import { ValidationError } from "@/presentation/http/validation/validator";
import type { IValidator } from "@/presentation/http/validation/validator";
import { z } from "zod";

export enum PersonType {
  PF = "PF",
  PJ = "PJ",
}

export enum ContractType {
  MUTUO = "MUTUO",
  SCP = "SCP",
}

const AddressSchema = z.object({
  street: z.string().optional(),
  number: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  postalCode: z.string().optional(),
  neighborhood: z.string().optional(),
  complement: z.string().optional(),
});

export const IndividualSchema = z.object({
  fullName: z.string().optional(),
  cpf: z.string().optional(),
  rg: z.string().optional(),
  issuingAgency: z.string().optional(),
  nationality: z.string().optional(),
  occupation: z.string().optional(),
  birthDate: z.string().optional(),
  email: z.string().email("Email inválido").optional(),
  phone: z.string().optional(),
  motherName: z.string().optional(),
  address: AddressSchema.optional(),
});

export const CompanySchema = z.object({
  corporateName: z.string().optional(),
  cnpj: z.string().optional(),
  type: z
    .nativeEnum(CompanyLegalType, {
      errorMap: () => ({ message: "Tipo de empresa inválido" }),
    })
    .optional(),
  size: z.string().optional(),
  address: AddressSchema.optional(),
  representative: IndividualSchema.optional(),
});

const InvestmentSchema = z.object({
  amount: z
    .union([z.string(), z.number()])
    .transform((val) => Number(val))
    .pipe(
      z
        .number({
          invalid_type_error: "Valor do investimento deve ser um número",
        })
        .min(1, "Valor do investimento deve ser maior que zero")
    )
    .optional(),
  monthlyRate: z
    .union([z.string(), z.number()])
    .transform((val) => Number(val))
    .pipe(
      z
        .number({
          invalid_type_error: "Taxa mensal deve ser um número",
        })
        .min(0, "Taxa mensal deve ser maior ou igual a zero")
    )
    .optional(),
  paymentMethod: z
    .nativeEnum(PaymentMethod, {
      errorMap: () => ({
        message:
          "Método de pagamento inválido. Tipos válidos: pix, bank_transfer, boleto",
      }),
    })
    .optional(),
  startDate: z.string().date("Data de início inválida").optional(),
  endDate: z.string().date("Data de término inválida").optional(),
  durationInMonths: z
    .union([z.string(), z.number()])
    .transform((val) => Number(val))
    .pipe(
      z
        .number({
          invalid_type_error: "O prazo deve ser um número",
        })
        .int("O prazo deve ser um número inteiro")
        .min(1, "Prazo deve ser maior que zero")
    )
    .optional(),
  profile: z
    .nativeEnum(InvestorProfile, {
      errorMap: () => ({
        message:
          "Perfil do investidor inválido. Tipos válidos: conservative, moderate, aggressive",
      }),
    })
    .optional(),
  isDebenture: z
    .union([z.boolean(), z.enum(["true", "false"])])
    .transform((val) => {
      if (typeof val === "boolean") return val;
      return val === "true";
    })
    .pipe(
      z.boolean({
        invalid_type_error: "Debenture deve ser um booleano",
      })
    )
    .optional(),
  quotaQuantity: z
    .union([z.string(), z.number()])
    .transform((val) => Number(val))
    .refine((val) => !isNaN(val), { message: "Quantidade de cotas inválida" })
    .optional(),
});

const BankAccountSchema = z.object({
  bank: z.string().optional(),
  agency: z.string().optional(),
  account: z.string().optional(),
  pix: z.string().optional(),
});

const EditContractSchema = z
  .object({
    contractId: z.string({ required_error: "ID do contrato é obrigatório" }),
    personType: z.nativeEnum(PersonType).optional(),
    contractType: z
      .nativeEnum(ContractType, {
        errorMap: () => ({
          message: "Tipo de contrato inválido. Tipos válidos: MUTUO ou SCP",
        }),
      })
      .optional(),
    investment: InvestmentSchema.optional(),
    bankAccount: BankAccountSchema.optional(),
    individual: IndividualSchema.optional(),
    company: CompanySchema.optional(),
  })
  .passthrough();

export class EditContractValidator implements IValidator<EditContractDTO> {
  validate(data: unknown): Either<ValidationError, EditContractDTO> {
    try {
      const result = EditContractSchema.parse(data);
      return right(result as EditContractDTO);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return left(new ValidationError(error.errors));
      }
      return left(new ValidationError([{ message: "Invalid data" }]));
    }
  }
}
