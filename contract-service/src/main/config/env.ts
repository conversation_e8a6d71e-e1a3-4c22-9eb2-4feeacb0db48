import { z } from 'zod';

// Esquema de validação com Zod
const envSchema = z.object({
  PORT: z
    .string()
    .transform(val => Number.parseInt(val, 10))
    .default('3000'),
  REDIS_PORT: z
    .string()
    .transform(val => Number.parseInt(val, 10))
    .default('6379'),
  REDIS_HOST: z.string().default('redis_contract'),
  REDIS_PASSWORD: z.string().default('REDIS_PASSWORD'),
  NODE_ENV: z
    .enum(['development', 'production', 'test'])
    .default('development'),
  AWS_ACCESS_KEY: z.string().default('AWS_ACCESS_KEY'),
  AWS_SECRET_KEY: z
    .string()
    .default('7Sr2QFP6xHBKhzRVYwi+hGqi3of04/gfxgA3FiWr'),
  AWS_REGION: z.string().default('us-east-1'),
  AWS_S3_BUCKET: z.string().default('icainvest-profile'),
  AWS_BUCKET_NAME: z.string().default('AWS_BUCKET_NAME'),
  ASSINATURA_EMAIL: z.string().default('ASSINATURA_EMAIL'),
  ASSINATURA_SENHA: z.string().default('ASSINATURA_SENHA'),
  ASSINATURA_LOGIN: z.string().default('ASSINATURA_LOGIN'),
  ASSINATURA_HASH_INTEGRACAO: z.string().default('ASSINATURA_HASH_INTEGRACAO'),
  API_ASSINATURA_URL: z.string().default('API_ASSINATURA_URL'),
  REPORT_API_URL: z.string().default('REPORT_API_URL'),
  EMAIL_API_URL: z.string().default('EMAIL_API_URL'),
  EMAIL_API_KEY: z.string().default('EMAIL_API_KEY'),
  CONTRACT_API_URL: z.string(),
  AWAITING_AUDIT_SIGNATURE_CRON: z.string().default('*/5 * * * *'),
  AWAITING_INVESTOR_SIGNATURE_CRON: z.string().default('*/5 * * * *'),
});

// Validação das variáveis de ambiente
const _env = envSchema.safeParse(process.env);

if (!_env.success) {
  console.error('❌ Invalid environment variables:', _env.error.format());
  throw new Error('Invalid environment variables.');
}

const env = _env.data;

export default env;
