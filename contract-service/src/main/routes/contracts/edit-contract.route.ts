import { fastifyRouteAdapter } from "@/main/adapters";
import { makeEditContractController } from "@/main/factories/controllers/contracts/edit-contract.factory";
import type { FastifyInstance } from "fastify";

export const editContractRoute = async (app: FastifyInstance) => {
  app.put(
    "/contracts/:id/edit",
    {
      config: {
        rawBody: true,
      },
    },
    fastifyRouteAdapter(makeEditContractController())
  );
};
