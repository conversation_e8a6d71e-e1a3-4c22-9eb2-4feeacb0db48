import { ContractStatus } from '@/domain/entities/contracts'
import type { ContractExpiredEvent } from '@/domain/events/contracts/contract-expired.event'
import type { IInvestmentContractRepository } from '@/domain/repositories'

export class HandleContractExpiration {
  constructor(private readonly repo: IInvestmentContractRepository) {}

  async handle(event: ContractExpiredEvent): Promise<void> {
    const contract = await this.repo.findById(event.payload.contractId)

    if (contract.isLeft()) {
      return
    }

    if (!contract.value) {
      console.warn(`[WARN] Contract not found: ${event.payload.contractId}`)
      return
    }

    const currentStatus = contract.value.getStatus()

    // Só atualiza se estiver em um estado válido para expiração
    if (currentStatus === ContractStatus.AWAITING_INVESTOR_SIGNATURE) {
      contract.value.markAsExpired()
      await this.repo.save(contract.value)
      console.log(`[INFO] Contract ${contract.value.id} marked as EXPIRED`)
    } else {
      console.log(
        `[SKIP] Contract ${contract.value.id} is not in a status to be expired. Current: ${currentStatus}`
      )
    }
  }
}
