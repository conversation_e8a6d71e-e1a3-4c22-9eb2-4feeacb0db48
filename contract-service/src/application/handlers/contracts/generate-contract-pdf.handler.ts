import type { IContractGenerator } from '@/application/interfaces/contract-generator'
import type { InvestmentContract } from '@/domain/entities/contracts'
import type { ContractCreatedEvent } from '@/domain/events/contracts/contract-created.event'
import { ContractPdfGeneratedEvent } from '@/domain/events/contracts/contract-pdf-generated.event'
import type { LoggerGateway } from '@/domain/gateways/logger.gateway'
import type { IStorageGateway } from '@/domain/gateways/storage.gateway'
import type { IInvestmentContractRepository } from '@/domain/repositories'
import { DomainEventMediator, type Either, left, right } from '@/domain/shared'

export class GenerateContractPdfHandler {
  constructor(
    private readonly repo: IInvestmentContractRepository,
    private readonly pdfApi: IContractGenerator,
    private readonly storageGateway: IStorageGateway,
    private readonly logger: LoggerGateway
  ) {}

  async handle(event: ContractCreatedEvent): Promise<void> {
    this.logger.info(
      `Iniciando o processamento do evento ContractCreatedEvent para o contrato com ID: ${event.payload.contractId}`
    )

    const contractResult = await this.repo.findById(event.payload.contractId)
    if (contractResult.isLeft()) {
      this.logger.error(
        `Erro ao buscar o contrato com ID: ${event.payload.contractId}`
      )
      return
    }
    if (!contractResult.value) {
      this.logger.error(
        `Contrato não encontrado para o ID: ${event.payload.contractId}`
      )
      return
    }

    const contract = contractResult.value
    this.logger.info(`Contrato encontrado. ID: ${contract.id}`)

    const fileResult = await this.pdfApi.generate(contract)
    if (fileResult.isLeft()) {
      contract.markAsFailedToGenerateContract()
      await this.repo.update(contract.id, contract)
      this.logger.error(
        `Erro na geração do PDF para o contrato com ID: ${contract.id}`
      )
      this.logger.error(`Erro: ${fileResult.value}`)
      return
    }

    const file = fileResult.value

    this.logger.info(
      `PDF gerado com sucesso para o contrato com ID: ${contract.id}`
    )
    const fileUrlResult = await this.generateFile(contract, file)

    if (fileUrlResult.isLeft()) {
      this.logger.info(
        `Falha ao fazer upload do contrato para o storage: ${fileResult.value}`
      )
      return
    }

    contract.appendContractUrl(fileUrlResult.value)

    await this.repo.update(contract.id, contract)
    this.logger.info(`Contrato atualizado com o PDF gerado. ID: ${contract.id}`)

    DomainEventMediator.dispatch(new ContractPdfGeneratedEvent(contract, file))
    this.logger.info(
      `Evento ContractPdfGeneratedEvent despachado para o contrato com ID: ${contract.id}`
    )
  }

  private async generateFile(
    investmentContract: InvestmentContract,
    reportFile: Buffer
  ): Promise<Either<Error, string>> {
    const path = `contracts/${investmentContract.getInvestor().getParty().getDocument()}_contrato_${Date.now()}`
    const mimeType = 'application/pdf'
    const bufferFile = Buffer.from(reportFile)

    const urlFileResult = await this.storageGateway.uploadFile(
      path,
      bufferFile,
      mimeType
    )

    if (urlFileResult.isLeft()) return left(urlFileResult.value)

    return right(urlFileResult.value)
  }
}
