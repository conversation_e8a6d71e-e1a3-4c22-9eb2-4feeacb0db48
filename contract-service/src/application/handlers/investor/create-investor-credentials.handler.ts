import type { IEmailService } from "@/application/interfaces/email-service";
import type { CreateInvestorCredentialsEvent } from "@/domain/events/investor/create-investor-credentials.event";
import type { LoggerGateway } from "@/domain/gateways/logger.gateway";
import type { IInvestmentContractRepository } from "@/domain/repositories";
import type { IInvestorRepository } from "@/domain/repositories/investor.repository";
import { generateRandomPassword } from "@/domain/utils/password-generator";
import { hashPassword } from "@/domain/utils/password-hash";

export class CreateInvestorCredentialsHandler {
  constructor(
    private readonly contractRepository: IInvestmentContractRepository,
    private readonly investorRepository: IInvestorRepository,
    private readonly emailService: IEmailService,
    private readonly logger: LoggerGateway
  ) {}

  async handle(event: CreateInvestorCredentialsEvent): Promise<void> {
    try {
      const contractId = event?.payload?.contractId;

      this.logger.info(
        `Iniciando criação de credenciais para o contrato ${contractId}`
      );

      // Buscar o contrato
      const contractResult = await this.contractRepository.findById(contractId);
      if (contractResult.isLeft() || !contractResult.value) {
        this.logger.error(`Contrato não encontrado: ${contractId}`);
        return;
      }

      const contract = contractResult.value;
      const investor = contract.getInvestor();
      const investorId = investor.id;

      // Verificar se o owner ja possui outros perfils (role como advisor ou broker)
      const hasOtherRoles = await this.investorRepository.hasOtherRolesForParty(
        investorId
      );
      let password: string | undefined = undefined;
      if (!hasOtherRoles) {
        this.logger.info(
          `O investidor ${investorId} não possui outros perfils`
        );

        // Gerar senha aleatória
        const newPassword = generateRandomPassword();
        this.logger.info(`Senha gerada para o investidor ${investorId}`);

        password = newPassword;
        // Criar hash da senha com bcrypt
        const encryptedPassword = await hashPassword(newPassword);
        this.logger.info(
          `Hash da senha criado para o investidor ${investorId}`
        );

        // Atualizar a senha do investidor com o hash
        const updateResult = await this.investorRepository.updatePassword(
          investorId,
          encryptedPassword
        );

        if (updateResult.isLeft()) {
          this.logger.error(
            `Erro ao atualizar senha do investidor ${investorId}: ${updateResult.value}`
          );
          return;
        }
      }
      // Enviar email com as credenciais
      if (investor.isIndividual()) {
        const emailResult = await this.emailService.sendWelcomeEmail(
          investor.getEmail(),
          investor.getName(),
          investor.getCpf(), // CPF como username
          password ?? " mesma senha do ICA Invest Contracts"
        );

        if (emailResult.isLeft()) {
          this.logger.error(
            `Erro ao enviar email para o investidor ${investorId}: ${emailResult.value}`
          );
          return;
        }

        this.logger.info(
          `Email enviado para o investidor individual ${investorId}`
        );
      } else if (investor.isCompany()) {
        const emailResult = await this.emailService.sendWelcomeEmail(
          investor.getEmail(),
          investor.getName(),
          investor.getCnpj() || "", // CNPJ como username
          password
        );

        if (emailResult.isLeft()) {
          this.logger.error(
            `Erro ao enviar email para o investidor ${investorId}: ${emailResult.value}`
          );
          return;
        }

        this.logger.info(
          `Email enviado para o investidor empresa ${investorId}`
        );
      } else {
        this.logger.error(
          `Tipo de investidor não reconhecido para ${investorId}`
        );
      }

      this.logger.info(
        `Credenciais criadas com sucesso para o contrato ${contractId}`
      );
    } catch (error) {
      this.logger.error(
        `Erro ao criar credenciais para o contrato ${event?.payload?.contractId}
: ${error}`
      );
    }
  }
}
