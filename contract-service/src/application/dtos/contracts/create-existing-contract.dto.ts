import type {
  AddressDTO,
  AdvisorAssignmentDTO,
  BankAccountDTO,
  CompanyLegalType,
  ContractType,
  InvestorProfile,
  PaymentMethod,
  PersonType,
} from '@/application/dtos/contracts/create-new-contract.dto'
import type {
  ContractFiles,
} from '@/domain/interfaces/file-upload'

export interface IndividualDTO {
  fullName: string
  cpf: string
  rg: string
  issuingAgency: string
  nationality: string
  occupation: string
  birthDate: string
  email: string
  phone: string
  motherName: string
  address: AddressDTO
}

export interface CompanyDTO {
  corporateName: string
  cnpj: string
  type: CompanyLegalType
  address: AddressDTO
  representative: IndividualDTO
}
export interface InvestmentDetailsDTO {
  amount: number
  monthlyRate: number
  durationInMonths: number
  paymentMethod: PaymentMethod
  startDate: string
  endDate: string
  profile: InvestorProfile
  quotaQuantity?: number
  isDebenture: boolean
}

export interface CreateExistingContractDTO {
  personType: PersonType
  contractType: ContractType
  brokerId: string
  investment: InvestmentDetailsDTO
  advisors: AdvisorAssignmentDTO[]
  bankAccount: BankAccountDTO
  individual?: IndividualDTO
  company?: CompanyDTO
  proofOfPayment: ContractFiles['proofOfPayment']
  personalDocument: ContractFiles['personalDocument']
  contract: ContractFiles['contract']
  proofOfResidence: ContractFiles['proofOfResidence']
  companyDocument?: ContractFiles['companyDocument']
}
