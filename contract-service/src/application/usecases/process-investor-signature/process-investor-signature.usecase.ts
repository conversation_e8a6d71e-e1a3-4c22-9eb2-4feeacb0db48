import { type ISignatureAPI, SignatureStatus } from '@/application/interfaces';
import { ContractStatus } from '@/domain/entities/contracts';
import type { LoggerGateway } from '@/domain/gateways/logger.gateway';
import type { IInvestmentContractRepository } from '@/domain/repositories';
import { type Either, left, right } from '@/domain/shared';

export class ProcessInvestorSignatureUseCase {
  constructor(
    private contractRepository: IInvestmentContractRepository,
    private signature: ISignatureAPI,
    private logger: LoggerGateway
  ) {}

  async execute(payload: {
    id: string;
    status: ContractStatus;
  }): Promise<Either<Error, ContractStatus>> {
    try {
      this.logger.info(
        `Iniciando processamento para o contrato ${payload.id} com status ${payload.status}`
      );

      const contractResult = await this.contractRepository.findById(payload.id);
      if (contractResult.isLeft()) {
        this.logger.error(
          `Erro ao buscar contrato ${payload.id}: ${
            contractResult.value.message || contractResult.value
          }`
        );
        return left(contractResult.value);
      }
      const contract = contractResult.value;
      if (!contract) {
        this.logger.error(`Contrato ${payload.id} não encontrado`);
        return left(new Error('Contrato não encontrado'));
      }
      this.logger.info(`Contrato ${contract.id} recuperado com sucesso`);

      this.logger.info(
        `Enviando contrato ${contract.id} para verificação de status de assinatura`
      );
      const statusSignatureResult = await this.signature.getStatus(contract);
      if (statusSignatureResult.isLeft()) {
        this.logger.error(
          `Erro ao obter status de assinatura para o contrato ${contract.id}: ${
            statusSignatureResult.value.message || statusSignatureResult.value
          }`
        );
        return left(statusSignatureResult.value);
      }
      const statusSignature = statusSignatureResult.value;
      this.logger.info(
        `Status de assinatura para o contrato ${contract.id}: ${statusSignature}`
      );

      const createdAt = contract.createdAt;
      const now = new Date();
      const hoursDiff =
        (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60);
      this.logger.info(
        `Horas decorridas desde a criação do contrato ${
          contract.id
        }: ${hoursDiff.toFixed(2)}`
      );
      if (statusSignature === SignatureStatus.SIGNED_BY_INVESTOR) {
        this.logger.info(
          `Contrato ${contract.id} assinado pelo investidor dentro do prazo, marcando como AWAITING_DEPOSIT`
        );
        contract.markAwaitingDeposit();
        await this.contractRepository.update(payload.id, contract);
        this.logger.info(
          `Contrato ${contract.id} atualizado para AWAITING_DEPOSIT`
        );
        return right(ContractStatus.AWAITING_DEPOSIT);
      }

      if (
        statusSignature === SignatureStatus.AWAITING_SIGNATURE &&
        hoursDiff >= 48
      ) {
        this.logger.info(
          `Contrato ${contract.id} excedeu 48 horas em AWAITING_SIGNATURE, marcando como expirado EXPIRED_BY_INVESTOR`
        );
        contract.markAsExpiredBySignature();
        await this.contractRepository.update(payload.id, contract);
        return right(ContractStatus.EXPIRED_BY_INVESTOR);
      }

      this.logger.info(
        `Contrato ${contract.id} com status AWAITING_INVESTOR_SIGNATURE`
      );

      return right(ContractStatus.AWAITING_INVESTOR_SIGNATURE);
    } catch (error) {
      this.logger.error(
        `Erro inesperado ao processar o contrato ${payload.id}: ${error}`
      );
      return left(error as Error);
    }
  }
}
