import type { ISignatureAPI } from '@/application/interfaces'
import { ContractStatus } from '@/domain/entities/contracts'
import type { LoggerGateway } from '@/domain/gateways/logger.gateway'
import type { IInvestmentContractRepository } from '@/domain/repositories'
import { type Either, left, right } from '@/domain/shared'

export class ProcessAwaitingDepositUseCase {
  constructor(
    private contractRepository: IInvestmentContractRepository,
    private signature: ISignatureAPI,
    private logger: LoggerGateway
  ) {}

  async execute(payload: {
    id: string
    status: ContractStatus
  }): Promise<Either<Error, ContractStatus>> {
    try {
      this.logger.info(
        `Iniciando processamento para o contrato ${payload.id} com status ${payload.status}`
      )

      const contractResult = await this.contractRepository.findById(payload.id)
      if (contractResult.isLeft()) {
        this.logger.error(
          `Erro ao buscar contrato ${payload.id}: ${
            contractResult.value.message || contractResult.value
          }`
        )
        return left(contractResult.value)
      }
      const contract = contractResult.value
      if (!contract) {
        this.logger.error(`Contrato ${payload.id} não encontrado`)
        return left(new Error('Contrato não encontrado'))
      }
      this.logger.info(`Contrato ${contract.id} recuperado com sucesso`)

      this.logger.info(
        `Enviando contrato ${contract.id} para verificação de status de assinatura`
      )
      const statusSignatureResult = await this.signature.getStatus(contract)
      if (statusSignatureResult.isLeft()) {
        this.logger.error(
          `Erro ao obter status de assinatura para o contrato ${contract.id}: ${
            statusSignatureResult.value.message || statusSignatureResult.value
          }`
        )
        return left(statusSignatureResult.value)
      }
      const statusSignature = statusSignatureResult.value
      this.logger.info(
        `Status de assinatura para o contrato ${contract.id}: ${statusSignature}`
      )

      const createdAt = contract.createdAt
      const now = new Date()
      const hoursDiff = (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60)
      this.logger.info(
        `Tempo decorrido desde a criação do contrato ${
          contract.id
        }: ${hoursDiff.toFixed(2)} horas`
      )

      if (hoursDiff >= 48) {
        this.logger.info(
          `Contrato ${contract.id} excedeu o limite de 48 horas, marcando como expirado por falha no comprovante de pagamento`
        )
        contract.markAsExpiredFailureProofPayment()
        await this.contractRepository.update(payload.id, contract)
        this.logger.info(
          `Contrato ${contract.id} atualizado para ${ContractStatus.EXPIRED_FAILURE_PROOF_PAYMENT}`
        )
        return right(ContractStatus.EXPIRED_FAILURE_PROOF_PAYMENT)
      }

      this.logger.info(
        `Contrato ${contract.id} permanece em status AWAITING_DEPOSIT`
      )
      return right(ContractStatus.AWAITING_DEPOSIT)
    } catch (error) {
      this.logger.error(
        `Erro inesperado no processamento do contrato ${payload.id}: ${error}`
      )
      return left(error as Error)
    }
  }
}
