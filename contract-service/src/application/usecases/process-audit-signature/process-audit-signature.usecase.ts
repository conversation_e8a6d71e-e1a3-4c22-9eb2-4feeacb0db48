import { type ISignatureAPI, SignatureStatus } from '@/application/interfaces'
import { ContractStatus } from '@/domain/entities/contracts'
import { CreateInvestorCredentialsEvent } from '@/domain/events/investor/create-investor-credentials.event'
import type { LoggerGateway } from '@/domain/gateways/logger.gateway'
import type { IInvestmentContractRepository } from '@/domain/repositories'
import { DomainEventMediator, type Either, left, right } from '@/domain/shared'

export class ProcessAuditSignatureUseCase {
  constructor(
    private contractRepository: IInvestmentContractRepository,
    private signature: ISignatureAPI,
    private logger: LoggerGateway
  ) {}

  async execute(payload: {
    id: string
    status: ContractStatus
  }): Promise<Either<Error, ContractStatus>> {
    try {
      this.logger.info(
        `Iniciando o processamento de auditoria de assinatura para o contrato: ${payload.id}`
      )

      const contractResult = await this.contractRepository.findById(payload.id)
      if (contractResult.isLeft()) {
        this.logger.error(
          `Erro ao buscar contrato ${payload.id}: ${
            contractResult.value.message || contractResult.value
          }`
        )
        return left(contractResult.value)
      }
      const contract = contractResult.value
      if (!contract) {
        this.logger.error(`Contrato ${payload.id} não encontrado`)
        return left(new Error('Contrato não encontrado'))
      }
      this.logger.info(`Contrato ${contract.id} encontrado com sucesso`)

      this.logger.info(
        `Enviando contrato ${contract.id} para verificação de status`
      )
      const statusSignatureResult = await this.signature.getStatus(contract)
      if (statusSignatureResult.isLeft()) {
        this.logger.error(
          `Erro ao obter status da assinatura para o contrato ${contract.id}: ${
            statusSignatureResult.value.message || statusSignatureResult.value
          }`
        )
        return left(statusSignatureResult.value)
      }
      const statusSignature = statusSignatureResult.value
      this.logger.info(
        `Status de assinatura para o contrato ${contract.id}: ${statusSignature}`
      )

      if (statusSignature === SignatureStatus.SIGNED_BY_ALL) {
        this.logger.info(
          `Contrato ${contract.id} foi assinado por todos. Atualizando status para ACTIVE.`
        )
        contract.markAsActive()
        await this.contractRepository.update(payload.id, contract)

        // Disparar evento para criar credenciais do investidor
        const createCredentialsEvent = new CreateInvestorCredentialsEvent(
          contract.id
        )
        DomainEventMediator.dispatch(createCredentialsEvent)

        this.logger.info(
          `Evento de criação de credenciais disparado para o contrato ${contract.id}`
        )

        this.logger.info(
          `Contrato ${contract.id} atualizado para ACTIVE com sucesso.`
        )
        return right(ContractStatus.ACTIVE)
      }

      const createdAt = contract.createdAt
      const now = new Date()
      const hoursDiff = (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60)
      this.logger.info(
        `Horas desde a criação do contrato ${contract.id}: ${hoursDiff}`
      )

      if (hoursDiff >= 48) {
        this.logger.info(
          `Contrato ${contract.id} expirado por auditoria após 48 horas.`
        )
        contract.markAsExpiredByAudit()
        await this.contractRepository.update(payload.id, contract)
        this.logger.info(
          `Contrato ${contract.id} atualizado para EXPIRED_BY_AUDIT com sucesso.`
        )
        return right(ContractStatus.EXPIRED_BY_AUDIT)
      }

      this.logger.info(
        `Contrato ${contract.id} permanece em status AWAITING_AUDIT_SIGNATURE.`
      )
      return right(ContractStatus.AWAITING_AUDIT_SIGNATURE)
    } catch (error) {
      this.logger.error(
        `Erro inesperado ao processar auditoria de assinatura: ${error}`
      )
      return left(error as Error)
    }
  }
}
