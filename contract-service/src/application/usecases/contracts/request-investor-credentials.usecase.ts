import type { IUseCase } from '@/application/interfaces'
import { ContractStatus } from '@/domain/entities/contracts'
import { CreateInvestorCredentialsEvent } from '@/domain/events/investor/create-investor-credentials.event'
import type { IInvestmentContractRepository } from '@/domain/repositories'
import { DomainEventMediator, type Either, left, right } from '@/domain/shared'

export interface RequestInvestorCredentialsDTO {
  contractId: string
}

export class RequestInvestorCredentialsUseCase
  implements
    IUseCase<RequestInvestorCredentialsDTO, Promise<Either<Error, void>>>
{
  constructor(
    private readonly contractRepository: IInvestmentContractRepository
  ) {}

  async execute(
    data: RequestInvestorCredentialsDTO
  ): Promise<Either<Error, void>> {
    // 1. Buscar o Contrato
    const contractResult = await this.contractRepository.findById(
      data.contractId
    )
    if (contractResult.isLeft()) {
      return left(new Error('Erro ao procurar contrato.'))
    }

    if (!contractResult.value) {
      return left(new Error('Contrato não encontrado.'))
    }

    const contract = contractResult.value

    if (contract.getStatus() !== ContractStatus.ACTIVE) {
      return left(
        new Error(
          `Não é possível solicitar credenciais para contrato com status ${contract.getStatus()}.`
        )
      )
    }

    const createCredentialsEvent = new CreateInvestorCredentialsEvent(
      contract.id
    )
    DomainEventMediator.dispatch(createCredentialsEvent)

    return right(undefined)
  }
}
