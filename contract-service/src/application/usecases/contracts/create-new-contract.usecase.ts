import {
  type CreateNewContractDTO,
  PersonType,
} from "@/application/dtos/contracts/create-new-contract.dto";
import type {
  ITransactionContext,
  IUnitOfWork,
} from "@/application/interfaces";
import type { IUseCase } from "@/application/interfaces/usecase";
import { InvestmentContract } from "@/domain/entities/contracts";
import { Company, Individual } from "@/domain/entities/parties";
import { Investor } from "@/domain/entities/user";
import { ContractCreatedEvent } from "@/domain/events/contracts/contract-created.event";
import type { LoggerGateway } from "@/domain/gateways/logger.gateway";
import type { IAdvisorRepository } from "@/domain/repositories";
import type { IInvestmentContractRepository } from "@/domain/repositories";
import type { IBrokerRepository } from "@/domain/repositories";
import type { IInvestorRepository } from "@/domain/repositories";
import { DomainEventMediator } from "@/domain/shared/domain-event-mediator";
import { type Either, left, right } from "@/domain/shared/either";
import { Money, PaymentMethod } from "@/domain/value-objects";
import { ContractType } from "@/domain/value-objects/contract-type.value-object";
import { Percentage } from "@/domain/value-objects/percentage.value-object";
import { Profile } from "@/domain/value-objects/profile.value-object";

export class CreateNewContractUseCase
  implements
    IUseCase<CreateNewContractDTO, Promise<Either<Error, InvestmentContract>>>
{
  constructor(
    private readonly unitOfWork: IUnitOfWork,
    private readonly contractRepository: IInvestmentContractRepository,
    private readonly advisorRepository: IAdvisorRepository,
    private readonly investorRepository: IInvestorRepository,
    private readonly brokerRepository: IBrokerRepository,
    private readonly logger: LoggerGateway
  ) {}

  async execute(
    dto: CreateNewContractDTO
  ): Promise<Either<Error, InvestmentContract>> {
    try {
      this.logger.info(
        `Início da execução do caso de uso: Criação de novo contrato. contractType: ${
          dto.contractType
        }, investorDocument: ${
          dto.individual ? dto.individual.cpf : dto.company?.cnpj
        }`
      );
      this.logger.info(JSON.stringify(dto, null, 2));

      const contract = await this.unitOfWork.runInTransaction(
        async (tx: ITransactionContext) => {
          this.logger.info("Transação iniciada para criação do novo contrato");

          // Passo 1: Criar ou encontrar investidor
          const investorResult = await this.createOrFindInvestor(dto, tx);
          if (investorResult.isLeft()) {
            this.logger.error(
              `Falha na criação/busca do investidor: ${investorResult.value}`
            );
            throw investorResult.value;
          }
          const investor = investorResult.value;

          // Passo 2: Encontrar broker
          const brokerResult = await this.findBroker(dto.brokerId, tx);
          if (brokerResult.isLeft()) {
            this.logger.error(
              `Broker não encontrado ou erro na busca. brokerId: ${dto.brokerId}, error: ${brokerResult.value}`
            );
            throw brokerResult.value;
          }
          const brokerId = brokerResult.value;
          this.logger.info(
            `Broker encontrado com sucesso. brokerId: ${brokerId}`
          );

          // Passo 3: Construir assessores
          const advisorsResult = await this.buildAdvisors(dto, tx);
          if (advisorsResult.isLeft()) {
            this.logger.error(
              `Erro na construção dos assessores: ${advisorsResult.value}`
            );
            throw advisorsResult.value;
          }
          const advisors = advisorsResult.value;
          this.logger.info(
            `Assessores construídos com sucesso. advisorsCount: ${advisors.length}`
          );

          // Passo 4: Construir contrato
          const contractResult = this.buildContract(dto, {
            investor,
            brokerId: brokerId,
            advisors,
          });
          if (contractResult.isLeft()) {
            this.logger.error(
              `Erro na construção do contrato: ${contractResult.value}`
            );
            throw contractResult.value;
          }
          const newContract = contractResult.value;
          this.logger.info(
            `Contrato construído com sucesso. contractId: ${newContract.id}`
          );

          // Passo 5: Salvar contrato
          const saveResult = await this.contractRepository.save(
            newContract,
            tx
          );
          if (saveResult.isLeft()) {
            this.logger.error(
              `Erro ao salvar o contrato. contractId: ${newContract.id}, error: ${saveResult.value}`
            );
            throw saveResult.value;
          }
          this.logger.info(
            `Contrato salvo com sucesso. contractId: ${newContract.id}`
          );

          // Registrar evento de domínio para disparo pós-commit
          tx.registerAfterCommit(() => {
            DomainEventMediator.dispatch(
              new ContractCreatedEvent(newContract.id)
            );
            this.logger.info(
              `Evento ContractCreatedEvent registrado para disparo pós-commit. contractId: ${newContract.id}`
            );
          });

          return newContract;
        }
      );

      return right(contract);
    } catch (err: any) {
      this.logger.error(
        `Erro na execução do caso de uso de criação de novo contrato: ${err}`
      );
      return left(err);
    }
  }

  /**
   * Cria ou encontra um investidor baseado no DTO
   * Este método contém toda a lógica de negócio para gerenciamento de investidores
   * @param dto - DTO de criação de contrato
   * @param tx - Contexto de transação
   * @returns Either com Investor ou Error
   */
  private async createOrFindInvestor(
    dto: CreateNewContractDTO,
    tx: ITransactionContext
  ): Promise<Either<Error, Investor>> {
    const document = dto.individual ? dto.individual.cpf : dto.company?.cnpj;
    if (!document)
      return left(new Error("Documento do investidor não fornecido"));

    this.logger.info(`Processando investidor com documento: ${document}`);

    // Passo 1: Verificar se o investidor já existe com papel de investidor
    const existingInvestor = await this.investorRepository.findByDocument(
      document,
      tx,
      dto.bankAccount.pix
    );
    if (existingInvestor.isLeft()) {
      return left(existingInvestor.value);
    }

    if (existingInvestor.value) {
      this.logger.info(
        `Investidor existente encontrado: ${existingInvestor.value.id}`
      );
      return right(existingInvestor.value);
    }

    // Passo 2: Criar novo investidor
    this.logger.info(`Criando novo investidor para documento: ${document}`);
    const newInvestor = await this.createInvestor(dto);
    if (newInvestor.isLeft()) {
      return left(newInvestor.value);
    }

    // Debug: Log dos dados do investidor criado
    this.logger.info(
      `Investidor criado em memória: ${newInvestor.value.getName()}`
    );

    // Passo 3: Salvar investidor no banco de dados
    const saveResult = await this.saveInvestorToDatabase(newInvestor.value, tx);
    if (saveResult.isLeft()) {
      return left(saveResult.value);
    }

    // Passo 4: Buscar investidor atualizado do banco para garantir dados consistentes
    const refreshedInvestor = await this.investorRepository.findByDocument(
      document,
      tx,
      dto.bankAccount.pix
    );
    if (refreshedInvestor.isLeft()) {
      return left(refreshedInvestor.value);
    }

    if (!refreshedInvestor.value) {
      return left(new Error("Investidor não encontrado após salvamento"));
    }

    // Debug: Log dos dados do investidor recuperado do banco
    this.logger.info(
      `Investidor recuperado do banco: ${refreshedInvestor.value.getName()}`
    );

    this.logger.info(
      `Investidor criado e atualizado com sucesso: ${refreshedInvestor.value.id}`
    );
    return right(refreshedInvestor.value);
  }

  /**
   * Salva investidor no banco de dados com todos os relacionamentos necessários
   * @param investor - Entidade investidor
   * @param tx - Contexto de transação
   * @returns Either com void ou Error
   */
  private async saveInvestorToDatabase(
    investor: Investor,
    tx: ITransactionContext
  ): Promise<Either<Error, void>> {
    try {
      const party = investor.getParty();

      if (investor.isCompany() && party instanceof Company)
        await this.saveCompanyInvestor(investor, party, tx);
      else if (party instanceof Individual)
        await this.saveIndividualInvestor(investor, party, tx);
      else return left(new Error("Tipo de investidor inválido"));

      return right(undefined);
    } catch (error) {
      return left(error as Error);
    }
  }

  /**
   * Salva um investidor empresa com todos os seus relacionamentos
   * @param investor - Entidade investidor
   * @param company - Parte empresa
   * @param tx - Contexto de transação
   */
  private async saveCompanyInvestor(
    investor: Investor,
    company: Company,
    tx: ITransactionContext
  ): Promise<void> {
    this.logger.info(`Salvando investidor empresa: ${company.getName()}`);

    const legalRepresentative = company.getLegalRepresentative();
    const { accountNumber, agency, bank } = investor.getAccount();

    // Passo 1: Encontrar ou criar representante legal
    let owner: any = await this.investorRepository.findOwnerByCpf(
      legalRepresentative.getCpf(),
      tx
    );

    if (!owner) {
      // Criar novo representante legal
      const address = legalRepresentative.getAddress();
      owner = await this.investorRepository.createOwner(
        {
          id: legalRepresentative.id,
          cpf: legalRepresentative.getCpf(),
          name: legalRepresentative.getName(),
          birthDate: legalRepresentative.getBirthDate(),
          email: legalRepresentative.getEmail(),
          motherName: legalRepresentative.getMotherName(),
          rg: legalRepresentative.getRg(),
          occupation: legalRepresentative.getOccupation(),
          phone: legalRepresentative.getPhone(),
          pep: legalRepresentative.getPep?.() || "",
          address: {
            postalCode: address.postalCode,
            city: address.city,
            neighborhood: address.neighborhood,
            number: address.number,
            state: address.state,
            street: address.street,
            complement: address.complement,
          },
        },
        tx
      );
    } else {
      // Atualizar representante legal existente se necessário
      await this.updateLegalRepresentativeIfNeeded(
        owner,
        legalRepresentative,
        tx
      );
    }

    // Passo 2: Encontrar ou criar empresa
    let business: any = await this.investorRepository.findBusinessByCnpj(
      company.getCnpj(),
      tx
    );

    if (!business) {
      // Criar nova empresa
      const companyAddress = company.getAddress();
      this.logger.info(`Criando nova empresa: ${company.getName()}`);
      business = await this.investorRepository.createBusiness(
        {
          id: company.id,
          cnpj: company.getCnpj(),
          companyName: company.getName(),
          email: company.getEmail(),
          ownerId: owner.id,
          type: company.getCompanyType().getValue(),
          size: company.getSize(),
          address: {
            postalCode: companyAddress.postalCode,
            city: companyAddress.city,
            neighborhood: companyAddress.neighborhood,
            number: companyAddress.number,
            state: companyAddress.state,
            street: companyAddress.street,
            complement: companyAddress.complement,
          },
        },
        tx
      );

      // Criar relacionamento proprietário-empresa
      await this.investorRepository.createOwnerBusinessRelation(
        business.id,
        owner.id,
        tx
      );
    } else {
      // Atualizar empresa existente se necessário
      this.logger.info(
        `Empresa existente encontrada: ${business.company_name}, atualizando se necessário`
      );

      await this.investorRepository.updateBusinessIfNeeded(
        business.id,
        {
          companyName: company.getName(),
          email: company.getEmail(),
        },
        tx
      );

      // Verificar se a relação owner_business_relation existe
      const hasOwnerBusinessRelation =
        await this.investorRepository.hasOwnerBusinessRelation(
          business.id,
          owner.id,
          tx
        );

      if (!hasOwnerBusinessRelation) {
        this.logger.info(
          `Criando relação owner_business_relation para empresa existente. businessId: ${business.id}, ownerId: ${owner.id}`
        );
        await this.investorRepository.createOwnerBusinessRelation(
          business.id,
          owner.id,
          tx
        );
      } else {
        this.logger.info(
          `Relação owner_business_relation já existe para empresa. businessId: ${business.id}`
        );
      }
    }

    // Passo 3: Criar ou atualizar conta bancária
    const hasExistingAccount = await this.investorRepository.hasBusinessAccount(
      business.id,
      tx
    );

    if (!hasExistingAccount) {
      await this.investorRepository.createBusinessAccount(
        business.id,
        {
          bank,
          number: accountNumber,
          branch: agency,
          type: "",
        },
        tx
      );
      this.logger.info(
        `Conta bancária criada para empresa. businessId: ${business.id}`
      );
    } else {
      await this.investorRepository.updateBusinessAccountIfNeeded(
        business.id,
        {
          bank,
          number: accountNumber,
          branch: agency,
          type: "",
        },
        tx
      );
      this.logger.info(
        `Conta bancária já existia e foi atualizada para empresa. businessId: ${business.id}`
      );
    }

    // Passo 4: Criar relacionamento de papel de investidor
    await this.investorRepository.createInvestorRoleRelation(
      investor.id,
      undefined,
      business.id,
      tx
    );

    this.logger.info(`Investidor empresa salvo com sucesso: ${investor.id}`);
  }

  /**
   * Salva um investidor individual com todos os seus relacionamentos
   * @param investor - Entidade investidor
   * @param individual - Parte individual
   * @param dto - DTO de criação de contrato
   * @param tx - Contexto de transação
   */
  private async saveIndividualInvestor(
    investor: Investor,
    individual: Individual,
    tx: ITransactionContext
  ): Promise<void> {
    this.logger.info(`Salvando investidor individual: ${individual.getName()}`);

    const { accountNumber, agency, bank } = investor.getAccount();

    // Passo 1: Encontrar ou criar proprietário
    let owner: any = await this.investorRepository.findOwnerByCpf(
      individual.getCpf(),
      tx
    );

    if (!owner) {
      // Criar novo proprietário
      const address = individual.getAddress();
      owner = await this.investorRepository.createOwner(
        {
          id: individual.id,
          cpf: individual.getCpf(),
          name: individual.getName(),
          birthDate: individual.getBirthDate(),
          email: individual.getEmail(),
          motherName: individual.getMotherName(),
          rg: individual.getRg(),
          occupation: individual.getOccupation(),
          phone: individual.getPhone(),
          pep: individual.getPep?.() || "",
          address: {
            postalCode: address.postalCode,
            city: address.city,
            neighborhood: address.neighborhood,
            number: address.number,
            state: address.state,
            street: address.street,
            complement: address.complement,
          },
        },
        tx
      );
    } else {
      // Atualizar proprietário existente com dados do DTO
      this.logger.info(
        `Proprietário existente encontrado: ${owner.name}, atualizando com dados do DTO`
      );

      await this.investorRepository.updateOwnerPersonalDataIfNeeded(
        owner.id,
        {
          name: individual.getName(),
          email: individual.getEmail(),
          phone: individual.getPhone(),
          rg: individual.getRg(),
          occupation: individual.getOccupation(),
          issuingAgency: individual.getIssuingAgency?.(),
          nationality: individual.getNationality?.(),
          motherName: individual.getMotherName(),
        },
        tx
      );

      // Atualizar endereço se necessário
      const address = individual.getAddress();
      const addressData = {
        postalCode: address.postalCode,
        city: address.city,
        neighborhood: address.neighborhood,
        number: address.number,
        state: address.state,
        street: address.street,
        complement: address.complement,
      };

      await this.investorRepository.updateOwnerAddressIfNeeded(
        owner.id,
        addressData,
        tx
      );

      this.logger.info(
        `Proprietário existente atualizado com dados do DTO. ownerId: ${owner.id}`
      );
    }

    // Passo 2: Criar ou atualizar conta bancária
    const hasExistingAccount = await this.investorRepository.hasOwnerAccount(
      owner.id,
      tx
    );

    if (!hasExistingAccount) {
      await this.investorRepository.createOwnerAccount(
        owner.id,
        {
          bank,
          number: accountNumber,
          branch: agency,
          type: "",
        },
        tx
      );
      this.logger.info(
        `Conta bancária criada para proprietário. ownerId: ${owner.id}`
      );
    } else {
      await this.investorRepository.updateOwnerAccountIfNeeded(
        owner.id,
        {
          bank,
          number: accountNumber,
          branch: agency,
          type: "",
        },
        tx
      );
      this.logger.info(
        `Conta bancária já existia e foi atualizada para proprietário. ownerId: ${owner.id}`
      );
    }

    // Passo 3: Criar relacionamento de papel de investidor
    await this.investorRepository.createInvestorRoleRelation(
      investor.id,
      owner.id,
      undefined,
      tx
    );

    this.logger.info(`Investidor individual salvo com sucesso: ${investor.id}`);
  }

  /**
   * Atualiza dados do representante legal se necessário
   * @param owner - Dados do proprietário existente
   * @param legalRepresentative - Entidade representante legal
   * @param tx - Contexto de transação
   */
  private async updateLegalRepresentativeIfNeeded(
    owner: any,
    legalRepresentative: Individual,
    tx: ITransactionContext
  ): Promise<void> {
    this.logger.info(
      `Verificando se representante legal precisa de atualização. ownerId: ${owner.id}, nome existente: ${owner.name}`
    );

    // Atualizar dados pessoais (apenas campos vazios)
    await this.investorRepository.updateOwnerPersonalDataIfNeeded(
      owner.id,
      {
        name: legalRepresentative.getName(),
        email: legalRepresentative.getEmail(),
        phone: legalRepresentative.getPhone(),
        rg: legalRepresentative.getRg(),
        occupation: legalRepresentative.getOccupation(),
        issuingAgency: legalRepresentative.getIssuingAgency?.(),
        nationality: legalRepresentative.getNationality?.(),
        motherName: legalRepresentative.getMotherName(),
      },
      tx
    );

    // Atualizar endereço (apenas campos vazios)
    const address = legalRepresentative.getAddress();
    const addressData = {
      postalCode: address.postalCode,
      city: address.city,
      neighborhood: address.neighborhood,
      number: address.number,
      state: address.state,
      street: address.street,
      complement: address.complement,
    };

    // Verificar se já existe um endereço para o proprietário
    const hasExistingAddress = owner.address && owner.address.length > 0;

    if (hasExistingAddress) {
      // Atualizar endereço existente (apenas campos vazios)
      await this.investorRepository.updateOwnerAddressIfNeeded(
        owner.id,
        addressData,
        tx
      );
      this.logger.info(
        `Endereço atualizado para representante legal. ownerId: ${owner.id}`
      );
    } else {
      // Criar novo endereço
      await this.investorRepository.createOwnerAddress(
        owner.id,
        addressData,
        tx
      );
      this.logger.info(
        `Endereço criado para representante legal. ownerId: ${owner.id}`
      );
    }
  }

  // Busca do broker utilizando o contexto transacional
  private async findBroker(
    id: string,
    tx: ITransactionContext
  ): Promise<Either<Error, string>> {
    this.logger.info(`Buscando broker com brokerId: ${id}`);
    const result = await this.brokerRepository.findById(id, tx);
    if (result.isLeft()) {
      this.logger.error(
        `Erro ao buscar broker. brokerId: ${id}, error: ${result.value}`
      );
      return left(result.value);
    }
    if (!result.value) {
      const error = new Error(`Broker ${id} não encontrado`);
      this.logger.error(`Broker não encontrado. brokerId: ${id}`);
      return left(error);
    }
    this.logger.info(`Broker encontrado. brokerId: ${id}`);
    return right(result.value);
  }

  // Busca e construção dos assessores utilizando o contexto transacional
  private async buildAdvisors(
    dto: CreateNewContractDTO,
    tx: ITransactionContext
  ): Promise<Either<Error, { id: string; rate: number }[]>> {
    const advisorIds = dto.advisors.map((a) => a.advisorId).join(", ");
    this.logger.info(
      `Iniciando construção dos assessores para o contrato. advisorIds: [${advisorIds}]`
    );
    const advisors: { id: string; rate: number }[] = [];
    for (const { advisorId, rate } of dto.advisors) {
      this.logger.info(`Buscando assessor. advisorId: ${advisorId}`);
      const result = await this.advisorRepository.findById(advisorId, tx);
      if (result.isLeft()) {
        this.logger.error(
          `Erro ao buscar assessor. advisorId: ${advisorId}, error: ${result.value}`
        );
        return left(result.value);
      }
      const advisorIdValue = result.value;
      if (!advisorIdValue) {
        const error = new Error(`Assessor ${advisorId} não encontrado`);
        this.logger.error(`Assessor não encontrado. advisorId: ${advisorId}`);
        return left(error);
      }

      this.logger.info(
        `Assessor encontrado e configurado. advisorId: ${advisorId}, participationRate: ${rate}`
      );
      advisors.push({ id: advisorIdValue, rate });
    }
    this.logger.info(
      `Todos os assessores foram construídos com sucesso. advisorsCount: ${advisors.length}`
    );
    return right(advisors);
  }

  // Construção do contrato a partir dos dados de entrada e das dependências
  private buildContract(
    dto: CreateNewContractDTO,
    deps: {
      brokerId: string;
      investor: Investor;
      advisors: { id: string; rate: number }[];
    }
  ): Either<Error, InvestmentContract> {
    this.logger.info(
      `Iniciando construção do contrato de investimento. investorId: ${deps.investor.id}, brokerId: ${deps.brokerId}, advisorsCount: ${deps.advisors.length}`
    );
    const {
      amount,
      monthlyRate,
      paymentMethod,
      endDate,
      profile,
      quotaQuantity,
    } = dto.investment;

    const amountResult = Money.create(amount);
    const profitabilityResult = Percentage.create(monthlyRate);
    const paymentMethodResult = PaymentMethod.create(paymentMethod);
    const contractTypeResult = ContractType.create(dto.contractType);
    const profileResult = Profile.create(profile);

    if (amountResult.isLeft()) {
      this.logger.error(
        `Erro na criação do valor do contrato. amount: ${amount}, error: ${amountResult.value}`
      );
      return left(amountResult.value);
    }
    if (profitabilityResult.isLeft()) {
      this.logger.error(
        `Erro na criação da rentabilidade do contrato. monthlyRate: ${monthlyRate}, error: ${profitabilityResult.value}`
      );
      return left(profitabilityResult.value);
    }
    if (paymentMethodResult.isLeft()) {
      this.logger.error(
        `Erro na criação do método de pagamento. paymentMethod: ${paymentMethod}, error: ${paymentMethodResult.value}`
      );
      return left(paymentMethodResult.value);
    }
    if (contractTypeResult.isLeft()) {
      this.logger.error(
        `Erro na criação do tipo de contrato. contractType: ${dto.contractType}, error: ${contractTypeResult.value}`
      );
      return left(contractTypeResult.value);
    }
    if (profileResult.isLeft()) {
      this.logger.error(
        `Erro na criação do perfil de investimento. profile: ${profile}, error: ${profileResult.value}`
      );
      return left(profileResult.value);
    }

    const contract = InvestmentContract.createNew({
      brokerId: deps.brokerId,
      investor: deps.investor,
      advisors: deps.advisors,
      amount: amountResult.value,
      profitability: profitabilityResult.value,
      paymentMethod: paymentMethodResult.value,
      type: contractTypeResult.value,
      profile: profileResult.value,
      endDate: new Date(endDate),
      addendums: [],
      isDebenture: false,
      quotaQuantity: quotaQuantity,
      durationInMonths: dto.investment.durationInMonths,
    });

    if (contract.isLeft()) {
      return left(contract.value);
    }
    this.logger.info(
      `Contrato de investimento construído com sucesso. contractId: ${contract.value.id}`
    );
    return right(contract.value);
  }

  // Criação do investidor a partir do DTO
  private async createInvestor(
    dto: CreateNewContractDTO
  ): Promise<Either<Error, Investor>> {
    this.logger.info(
      `Iniciando criação do investidor. personType: ${dto.personType}`
    );
    const { personType, individual, company } = dto;

    if (personType === PersonType.PF && individual) {
      const result = Investor.createIndividual(
        crypto.randomUUID(),
        individual.fullName,
        individual.email,
        individual.phone,
        individual.cpf,
        new Date(individual.birthDate),
        individual.motherName,
        individual.address,
        dto.bankAccount,
        individual.rg,
        individual.occupation,
        individual.issuingAgency,
        individual.nationality
      );
      if (result.isLeft()) {
        this.logger.error(
          `Erro ao criar investidor Pessoa Física. error: ${result.value}`
        );
      } else {
        this.logger.info(
          `Investidor Pessoa Física criado com sucesso. investorId: ${result.value.id}`
        );
      }
      return result;
    }

    if (personType === PersonType.PJ && company) {
      this.logger.info(
        `Iniciando criação do investidor do tipo Pessoa Jurídica. corporateName: ${company.corporateName}`
      );
      const legalRepResult = Individual.create(
        crypto.randomUUID(),
        company.representative.fullName,
        {
          address: company.representative.address,
          birthDate: new Date(company.representative.birthDate),
          cpf: company.representative.cpf,
          email: company.representative.email,
          motherName: company.representative.motherName,
          phone: company.representative.phone,
          rg: company.representative.rg,
          issuingAgency: company.representative.issuingAgency,
          nationality: company.representative.nationality,
          occupation: company.representative.occupation,
        }
      );

      if (legalRepResult.isLeft()) {
        this.logger.error(
          `Erro ao criar representante legal para investidor Pessoa Jurídica. error: ${legalRepResult.value}`
        );
        return left(legalRepResult.value);
      }

      const result = Investor.createCompany(
        crypto.randomUUID(),
        company.corporateName,
        company.representative.email,
        company.representative.phone,
        company.cnpj,
        legalRepResult.value,
        company.address,
        company.type,
        dto.bankAccount
      );
      if (result.isLeft()) {
        this.logger.error(
          `Erro ao criar investidor Pessoa Jurídica. error: ${result.value}`
        );
      } else {
        this.logger.info(
          `Investidor Pessoa Jurídica criado com sucesso. investorId: ${result.value.id}`
        );
      }
      return result;
    }

    const error = new Error(
      "Dados do investidor são inválidos para o tipo informado"
    );
    this.logger.error(`Erro na criação do investidor: ${error.message}`);
    return left(error);
  }
}
