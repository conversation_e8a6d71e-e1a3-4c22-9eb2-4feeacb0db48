import type { IUseCase } from '@/application/interfaces/usecase'
import type { InvestmentContract } from '@/domain/entities/contracts'
import type { IInvestmentContractRepository } from '@/domain/repositories'
import { type Either, left, right } from '@/domain/shared'
import type { PaginatedResult } from '@/domain/shared/paginated-result'

export class ListContractsUseCase
  implements
    IUseCase<
      { userId: string; page?: number; limit?: number },
      PaginatedResult<InvestmentContract>
    >
{
  constructor(
    private readonly investmentContractRepo: IInvestmentContractRepository
  ) {}

  async execute({
    userId,
    page = 1,
    limit = 10,
  }: { userId: string; page?: number; limit?: number }): Promise<
    Either<Error, PaginatedResult<InvestmentContract>>
  > {
    const contracts = await this.investmentContractRepo.findPaginated(
      userId,
      page,
      limit
    )

    if (contracts.isLeft()) {
      return left(contracts.value)
    }

    return right(contracts.value)
  }
}
