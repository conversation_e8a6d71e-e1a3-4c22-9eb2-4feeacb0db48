import type { IUseCase } from "@/application/interfaces/usecase";
import { ContractStatus } from "@/domain/entities/contracts/investment-contract.entity";
import type { LoggerGateway } from "@/domain/gateways/logger.gateway";
import type { IContractDeletionRepository } from "@/domain/repositories";
import { type Either, left, right } from "@/domain/shared";

import { DeleteContractDTO } from "@/application/dtos/contracts/deleted-contract.dto";
import { SignatureAdapter } from "@/main/adapters/rbm/signature.adapter";
import { IUnitOfWork } from "@/application/interfaces";
import { IContractRepository } from "@/domain/repositories/contract.repository";

interface DeleteContractResponse {
  id: string;
  status: ContractStatus;
}

export class DeleteContractUseCase
  implements
    IUseCase<DeleteContractDTO, Promise<Either<Error, DeleteContractResponse>>>
{
  constructor(
    private readonly contractRepository: IContractRepository,
    private readonly contractDeletionRepository: IContractDeletionRepository,
    private readonly signatureApi: SignatureAdapter,
    private readonly unitOfWork: IUnitOfWork,
    private readonly logger: LoggerGateway
  ) {}

  async execute(
    data: DeleteContractDTO
  ): Promise<Either<Error, DeleteContractResponse>> {
    try {
      const result: Either<Error, DeleteContractResponse> =
        await this.unitOfWork.runInTransaction(async (tx) => {
          this.logger.info(`Iniciando exclusão do contrato ${data.contractId}`);

          const contractResult = await this.contractRepository.findById(
            data.contractId
          );

          if (!contractResult || !contractResult.value)
            return left(new Error("Contrato não encontrado"));

          if (contractResult.isLeft()) return left(contractResult.value);

          const contract = contractResult.value;

          if (contract.status === ContractStatus.ACTIVE)
            return left(new Error("Contrato ativo, não pode ser deletado"));

          if (contract.status === ContractStatus.DELETED)
            return left(new Error("Contrato já deletado"));

          if (contract.externalId) {
            const documentExist = await this.signatureApi.documentExist(
              contract.externalId
            );

            if (documentExist.isLeft()) {
              this.logger.error(
                `Erro ao verificar documento na Dimensa ${contract.id}`
              );
              return left(documentExist.value);
            }

            if (documentExist.value) {
              const statusSignature = await this.signatureApi.deleteContract(
                contract.externalId
              );

              if (statusSignature.isLeft()) {
                this.logger.error(
                  `Erro ao deletar contrato na Dimensa ${contract.id}`
                );
                return left(statusSignature.value);
              }
            }
          }

          const deletionResult = await this.contractDeletionRepository.create(
            {
              contractId: contract.id,
              deletedById: data.userId,
              reason: data.reason,
            },
            tx
          );

          if (deletionResult.isLeft()) {
            this.logger.error(
              `Erro ao criar registro de deleção do contrato ${contract.id}: ${deletionResult.value.message}`
            );
            throw deletionResult.value;
          }

          const updateContractResult =
            await this.contractRepository.updateStatus(
              contract.id,
              ContractStatus.DELETED,
              tx
            );

          if (updateContractResult.isLeft()) {
            this.logger.error(
              `Erro ao atualizar contrato ${contract.id}: ${updateContractResult.value.message}`
            );
            throw updateContractResult.value;
          }

          this.logger.info(
            `Contrato deletado com sucesso ${contract.id} with status ${ContractStatus.DELETED}`
          );

          return right({
            id: data.contractId,
            status: ContractStatus.DELETED,
          });
        });

      return result;
    } catch (error) {
      this.logger.error(
        `Erro ao deletar contrato: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
      return left(new Error("Erro ao deletar contrato"));
    }
  }
}
