export interface HttpResponse<TResult = any> {
  statusCode: number
  data: TResult
  headers?: Record<string, string>
}

export interface HttpClient {
  get<TResult = any>(
    url: string,
    options?: {
      headers?: Record<string, string>
      params?: Record<string, string | number>
    }
  ): Promise<HttpResponse<TResult>>

  post<TData = any, TResult = any>(
    url: string,
    body: TData,
    options?: {
      headers?: Record<string, string>
      responseType?: string
    }
  ): Promise<HttpResponse<TResult>>

  put<TData = any, TResult = any>(
    url: string,
    body: TData,
    options?: {
      headers?: Record<string, string>
    }
  ): Promise<HttpResponse<TResult>>

  patch<TData = any, TResult = any>(
    url: string,
    body: TData,
    options?: {
      headers?: Record<string, string>
    }
  ): Promise<HttpResponse<TResult>>

  delete<TResult = any>(
    url: string,
    options?: {
      headers?: Record<string, string>
      params?: Record<string, string | number>
    }
  ): Promise<HttpResponse<TResult>>
}
