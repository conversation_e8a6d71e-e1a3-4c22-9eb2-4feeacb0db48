// Define os níveis de isolamento suportados
export enum TransactionIsolationLevel {
  READ_UNCOMMITTED = 'READ UNCOMMITTED',
  READ_COMMITTED = 'READ COMMITTED',
  REPEATABLE_READ = 'REPEATABLE READ',
  SERIALIZABLE = 'SERIALIZABLE',
}

// Essa interface representa um contexto transacional abstrato.
// Ela permite o controle manual do commit/rollback e o registro de ações
// que serão executadas após um commit bem-sucedido.
export interface ITransactionContext {
  commit(): Promise<void>
  rollback(): Promise<void>
  registerAfterCommit(action: () => Promise<void> | void): void
}
