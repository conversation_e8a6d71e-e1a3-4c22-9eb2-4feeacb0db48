export class ValidatorRules {
  static notEmpty(value: string, field: string): void {
    if (!value || value.trim() === '') {
      throw new Error(`[${field}] não pode estar vazio.`)
    }
  }

  static minLength(value: string, length: number, field: string): void {
    if (value.length < length) {
      throw new Error(`[${field}] deve ter pelo menos ${length} caracteres.`)
    }
  }

  static maxLength(value: string, length: number, field: string): void {
    if (value.length > length) {
      throw new Error(`[${field}] deve ter no máximo ${length} caracteres.`)
    }
  }

  static isEmail(value: string, field: string): void {
    const emailRegex = /^[\w-.]+@([\w-]+\.)+[\w-]{2,4}$/
    if (!emailRegex.test(value)) {
      throw new Error(`[${field}] deve ser um e-mail válido.`)
    }
  }

  static isPositiveNumber(value: number, field: string): void {
    if (value <= 0) {
      throw new Error(`[${field}] deve ser um número positivo.`)
    }
  }
}
