import type { BaseEntityProps } from './base-entity'
import type { DomainEvent } from './domain-event'
import { Entity } from './entity'
import { Notification } from './notification'

export abstract class AggregateRoot<
  T extends BaseEntityProps,
> extends Entity<T> {
  private domainEvents: DomainEvent[] = []
  private notification: Notification = new Notification()

  addDomainEvent(event: DomainEvent): void {
    this.domainEvents.push(event)
  }

  getDomainEvents(): DomainEvent[] {
    return this.domainEvents
  }

  clearDomainEvents(): void {
    this.domainEvents = []
  }

  getNotification(): Notification {
    return this.notification
  }

  hasErrors(): boolean {
    return this.notification.hasErrors()
  }
}
