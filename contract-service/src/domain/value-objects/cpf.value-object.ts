import { type Either, left, right } from '../shared';

export class Cpf {
  private constructor(private readonly _value: string) {}

  static create(value: string): Either<Error, Cpf> {
    const cleaned = value.replace(/[^\d]/g, '');

    // if (!Cpf.isValid(cleaned)) {
    //   return left(Error(`Invalid CPF: ${cleaned}.`))
    // }

    return right(new Cpf(cleaned));
  }

  get value(): string {
    return this._value;
  }

  private static isValid(cpf: string): boolean {
    if (!cpf || cpf.length !== 11 || /^(\d)\1{10}$/.test(cpf)) return false;

    const digits = cpf.split('').map((d) => Number.parseInt(d));

    for (let i = 9; i <= 10; i++) {
      const sum = digits
        .slice(0, i)
        .reduce((acc, d, idx) => acc + d * (i + 1 - idx), 0);
      const expected = ((sum * 10) % 11) % 10;
      if (expected !== digits[i]) return false;
    }

    return true;
  }

  formatted(): string {
    return this._value.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
  }
}
