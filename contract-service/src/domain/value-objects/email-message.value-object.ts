export enum EmailStatus {
  PENDING = 'PENDING',
  SENT = 'SENT',
  FAILED = 'FAILED',
}

export class EmailMessage {
  private constructor(
    private readonly from: string,
    private readonly replyTo?: string,
    private readonly body?: string,
    private readonly bodyType?: string
  ) {}

  static create(
    from: string,
    replyTo?: string,
    body?: string,
    bodyType?: string
  ): EmailMessage {
    return new EmailMessage(from, replyTo, body, bodyType)
  }

  getFrom(): string {
    return this.from
  }

  getReplyTo(): string | undefined {
    return this.replyTo
  }

  getBody(): string | undefined {
    return this.body
  }

  getBodyType(): string | undefined {
    return this.bodyType
  }
}
