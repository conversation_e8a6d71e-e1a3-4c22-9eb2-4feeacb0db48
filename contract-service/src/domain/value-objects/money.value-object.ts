import { type Either, left, right } from '../shared'

export class Money {
  private constructor(private readonly value: number) {}

  static create(value: number): Either<Error, Money> {
    if (value <= 0) return left(new Error('Amount must be positive'))
    return right(new Money(value))
  }

  get amount(): number {
    return this.value
  }

  formatBRL(): string {
    return this.value.toLocaleString('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    })
  }
}
