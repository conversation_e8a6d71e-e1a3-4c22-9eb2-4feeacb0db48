import { type Either, left, right } from '../shared';

export class Cnpj {
  private constructor(private readonly _value: string) {}

  get value(): string {
    return this._value;
  }

  static create(value: string): Either<Error, Cnpj> {
    const cleaned = value.replace(/[^\d]/g, '');

    // if (!Cnpj.isValid(cleaned)) {
    //   left(Error('Invalid CNPJ.'))
    // }

    return right(new Cnpj(cleaned));
  }

  private static isValid(cnpj: string): boolean {
    if (!cnpj || cnpj.length !== 14 || /^(\d)\1{13}$/.test(cnpj)) return false;

    const digits = cnpj.split('').map(Number);
    const validate = (base: number[]) => {
      const weights =
        base.length === 12
          ? [5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2]
          : [6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];

      const sum = base.reduce((acc, val, idx) => acc + val * weights[idx], 0);
      const mod = sum % 11;
      return mod < 2 ? 0 : 11 - mod;
    };

    const firstCheck = validate(digits.slice(0, 12));
    const secondCheck = validate([...digits.slice(0, 12), firstCheck]);

    return digits[12] === firstCheck && digits[13] === secondCheck;
  }

  formatted(): string {
    return this._value.replace(
      /^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})$/,
      '$1.$2.$3/$4-$5',
    );
  }
}
