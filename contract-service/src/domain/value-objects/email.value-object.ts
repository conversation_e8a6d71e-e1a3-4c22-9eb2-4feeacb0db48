import { type Either, left, right } from '../shared'

export class Email {
  private constructor(private readonly _value: string) {}

  static create(value: string): Either<Error, Email> {
    const emailRegex = /^[\w-.]+@([\w-]+\.)+[\w-]{2,4}$/
    // if (!emailRegex.test(value)) {
    //   return left(Error('Invalid email address.'))
    // }

    return right(new Email(value))
  }

  get value(): string {
    return this._value
  }
}
