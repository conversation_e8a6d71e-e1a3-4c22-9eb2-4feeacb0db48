export class FinancialCalendar {
  private static holidays: Set<string> = new Set([
    '2025-01-01',
    '2025-04-18',
    '2025-05-01',
    '2025-12-25',
  ])

  static isWeekend(date: Date): boolean {
    const day = date.getDay()
    return day === 0 || day === 6
  }

  static isHoliday(date: Date): boolean {
    return FinancialCalendar.holidays.has(date.toISOString().split('T')[0])
  }

  static nextBusinessDay(date: Date): Date {
    const nextDate = new Date(date)
    while (
      FinancialCalendar.isWeekend(nextDate) ||
      FinancialCalendar.isHoliday(nextDate)
    ) {
      nextDate.setDate(nextDate.getDate() + 1)
    }
    return nextDate
  }
}
