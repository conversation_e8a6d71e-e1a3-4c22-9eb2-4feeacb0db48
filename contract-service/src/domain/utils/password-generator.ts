/**
 * Gera uma senha aleatória com os requisitos especificados:
 * - 8 dígitos
 * - Contém letras maiúsculas
 * - Contém letras minúsculas
 * - Contém números
 * - Contém pelo menos um caractere especial (@, #, %)
 */
export function generateRandomPassword(): string {
  const uppercaseChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const lowercaseChars = 'abcdefghijklmnopqrstuvwxyz';
  const numberChars = '0123456789';
  const specialChars = '@#%';

  // Garantir pelo menos um caractere de cada tipo
  const password = [
    uppercaseChars[Math.floor(Math.random() * uppercaseChars.length)],
    lowercaseChars[Math.floor(Math.random() * lowercaseChars.length)],
    numberChars[Math.floor(Math.random() * numberChars.length)],
    specialChars[Math.floor(Math.random() * specialChars.length)],
  ];

  // Completar até 8 caracteres com uma mistura de todos os tipos
  const allChars = uppercaseChars + lowercaseChars + numberChars + specialChars;
  while (password.length < 8) {
    password.push(allChars[Math.floor(Math.random() * allChars.length)]);
  }

  // Embaralhar a senha para não ter um padrão previsível
  for (let i = password.length - 1; i > 0; i--) {
    const j = crypto.getRandomValues(new Uint32Array(1))[0] % (i + 1);

    [password[i], password[j]] = [password[j], password[i]];
  }

  return password.join('');
}
