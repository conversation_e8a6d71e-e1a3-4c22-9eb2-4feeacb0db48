import type { Cnpj, Email } from "@/domain/value-objects";
import type { Address } from "@/domain/value-objects";
import type { CompanyType } from "@/domain/value-objects";
import type { Phone } from "@/domain/value-objects";
import type { Individual } from "./individual.entity";
import { Party } from "./party.entity";

export class Company extends Party {
  constructor(
    id: string,
    name: string,
    email: Email,
    phone: Phone,
    private cnpj: Cnpj,
    private legalRepresentative: Individual,
    private address: Address,
    private companyType: CompanyType,
    private size: string
  ) {
    super(
      {
        name,
        email,
        phone,
        address,
      },
      id
    );
  }
  getDocument(): string {
    return this.cnpj.value;
  }

  getCpf(): string {
    return this.legalRepresentative.getCpf();
  }
  getMotherName(): string {
    return this.legalRepresentative.getMotherName();
  }
  getBirthDate(): Date {
    return this.legalRepresentative.getBirthDate();
  }
  getRg(): string | undefined {
    return this.legalRepresentative.getRg();
  }

  getFormattedCpf(): string {
    return this.legalRepresentative.getFormattedCpf();
  }

  getSize(): string {
    return this.size;
  }

  getCnpj(): string {
    return this.cnpj.value;
  }

  getFormattedCnpj(): string {
    return this.cnpj.formatted();
  }

  getLegalRepresentative(): Individual {
    return this.legalRepresentative;
  }

  getOccupation(): string | undefined {
    return this.legalRepresentative.getOccupation();
  }

  getAddress(): Address {
    return this.address;
  }

  getCompanyType() {
    return this.companyType;
  }

  updateCompany(
    name: string,
    email: Email,
    phone: Phone,
    cnpj: Cnpj,
    legalRepresentative: Individual,
    address: Address,
    companyType: CompanyType
  ): void {
    this.updateData(name, email, phone);
    this.cnpj = cnpj;
    this.legalRepresentative = legalRepresentative;

    this.address = address;
    this.companyType = companyType;
  }

  updateRepresentativeAddress(address: Address): void {
    this.legalRepresentative.updateAddress(address);
  }
}
