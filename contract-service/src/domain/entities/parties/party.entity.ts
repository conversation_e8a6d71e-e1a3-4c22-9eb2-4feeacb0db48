import type { BaseEntityProps } from "@/domain/shared/base-entity";
import { Entity } from "@/domain/shared/entity";
import type { Address } from "@/domain/value-objects/address.value-object";
import type { Email } from "@/domain/value-objects/email.value-object";
import type { Phone } from "@/domain/value-objects/phone.value-object";

export interface PartyProps extends BaseEntityProps {
  name: string;
  email: Email;
  phone: Phone;
  address: Address;
  motherName?: string;
  pep?: string;
}

export abstract class Party extends Entity<PartyProps> {
  protected constructor(props: PartyProps, id: string) {
    super(props, id); // 🧠 id é obrigatório aqui
  }

  getName(): string {
    return this.props.name;
  }

  getPhone(): string {
    return this.props.phone.value;
  }

  getPep(): string {
    return this.props.pep ?? "";
  }

  getEmail(): string {
    return this.props.email.value;
  }

  updateData(name: string, email: Email, phone: Phone): void {
    this.props.name = name;
    this.props.email = email;
    this.props.phone = phone;
  }

  updateAddress(address: Address): void {
    this.props.address = address;
  }

  updateMotherName(motherName: string): void {
    this.props.motherName = motherName;
  }

  abstract getBirthDate(): Date;
  abstract getDocument(): string;
  abstract getCpf(): string;
  abstract getFormattedCpf(): string;
  abstract getOccupation(): string | undefined;
  abstract getAddress(): Address;
  abstract getRg(): string | undefined;
  abstract getMotherName(): string;
}
