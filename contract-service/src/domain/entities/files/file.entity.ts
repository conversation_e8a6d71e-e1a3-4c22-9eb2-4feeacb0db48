import type { BaseEntityProps } from '@/domain/shared/base-entity';
import { Entity } from '@/domain/shared/entity';

export interface FileProps extends BaseEntityProps {
  filename: string;
  url: string;
  type: string;
}

export class File extends Entity<FileProps> {
  private constructor(props: FileProps, id?: string) {
    super(props, id);
  }

  static create(props: FileProps, id?: string): File {
    if (!props.filename) {
      throw new Error('Filename is required');
    }
    if (!props.url) {
      throw new Error('URL is required');
    }
    if (!props.type) {
      throw new Error('Type is required');
    }

    return new File(props, id);
  }

  get filename(): string {
    return this.props.filename;
  }

  get url(): string {
    return this.props.url;
  }

  get type(): string {
    return this.props.type;
  }

  rename(newFilename: string): void {
    if (!newFilename.trim()) {
      throw new Error('Filename cannot be empty');
    }
    this.props.filename = newFilename;
  }
}
