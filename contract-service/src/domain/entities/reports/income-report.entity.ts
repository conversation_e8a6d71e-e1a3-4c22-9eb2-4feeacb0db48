import type { InvestmentContract } from '@/domain/entities/contracts'
import { AggregateRoot } from '@/domain/shared'
import type { BankAccount } from '@/domain/value-objects'
import type { Investor } from '../user'
import type { IncomeReportEmail } from './income-report-email.entity'

export enum IncomeReportStatus {
  DRAFT = 'DRAFT',
  SENDING_TO_REPORT_API = 'SENDING_TO_REPORT_API',
  REPORT_CREATED = 'REPORT_CREATED',
  SEND_EMAIL = 'SEND_EMAIL',
  ERROR_REPORT_API = 'ERROR_REPORT_API',
  ERROR_UPLOAD_REPORT_FILE = 'ERROR_UPLOAD_REPORT_FILE',
  ERROR_CREATE_EMAIL = 'ERROR_CREATE_EMAIL',
}

interface IncomeReportProps {
  investor: Investor
  year: number
  contractYields: {
    contract: InvestmentContract
    amount: number
    valueApplicateContract: number
    valueInvestimentAddedum: number
    valueInvestimentContract: number
  }[]
  emails: IncomeReportEmail[]
  status: IncomeReportStatus
  fileId?: string | null
  fileUrl?: string | null
  createdAt?: Date
  updatedAt?: Date
}

export class IncomeReport extends AggregateRoot<IncomeReportProps> {
  private constructor(props: IncomeReportProps, id?: string) {
    super(props, id)
  }

  static createNew(investor: Investor, year: number): IncomeReport {
    return new IncomeReport({
      investor,
      year,
      contractYields: [],
      emails: [],
      status: IncomeReportStatus.DRAFT,
    })
  }

  static createFromExisting(
    props: IncomeReportProps,
    id: string
  ): IncomeReport {
    return new IncomeReport(props, id)
  }

  addFileId(fileId: string): void {
    this.props.fileId = fileId
  }

  addContract(
    contract: InvestmentContract,
    amount: number,
    valueApplicateContract: number,
    valueInvestimentAddedum: number,
    valueInvestimentContract: number
  ): void {
    this.props.contractYields.push({
      contract,
      amount,
      valueApplicateContract,
      valueInvestimentAddedum,
      valueInvestimentContract,
    })
    this.touch()
  }

  setFileUrl(url: string) {
    this.props.fileUrl = url
  }

  addFileUrl(fileUrl: string): void {
    this.props.fileUrl = fileUrl
  }

  getFileUrl(): string | null {
    return this.props.fileUrl ?? null
  }

  getBankAccount(): BankAccount {
    return this.props.investor.getAccount()
  }

  getBalance(): {
    applicationBalanceYear: number
    valueYieldYear: number
    valueInvestimentAddedum: number
    valueInvestimentContract: number
  } {
    const valueInvestimentAddedum =
      this.props.contractYields[0].valueInvestimentAddedum ?? 0
    const applicationBalanceYear = this.props.contractYields.reduce(
      (acc, curr) => acc + curr.valueApplicateContract,
      0
    )

    const valueYieldYear = this.props.contractYields.reduce(
      (acc, curr) => acc + curr.amount,
      0
    )

    const valueInvestimentContract =
      this.props.contractYields[0].valueInvestimentContract ?? 0
    return {
      applicationBalanceYear,
      valueYieldYear,
      valueInvestimentAddedum,
      valueInvestimentContract,
    }
  }

  addEmail(email: IncomeReportEmail): void {
    this.props.emails.push(email)
    this.touch()
  }

  markAsErrorCreateEmail(): void {
    this.props.status = IncomeReportStatus.ERROR_CREATE_EMAIL
    this.touch()
  }
  markAsErrorUploadReportFile(): void {
    this.props.status = IncomeReportStatus.ERROR_UPLOAD_REPORT_FILE
    this.touch()
  }
  markAsErrorReportApi(): void {
    this.props.status = IncomeReportStatus.ERROR_REPORT_API
    this.touch()
  }
  markAsSentEmail(): void {
    this.props.status = IncomeReportStatus.SEND_EMAIL
    this.touch()
  }

  markAsSendingToReportApi(): void {
    this.props.status = IncomeReportStatus.SENDING_TO_REPORT_API
    this.touch()
  }
  markAsCreatedReportApi(): void {
    this.props.status = IncomeReportStatus.REPORT_CREATED
    this.touch()
  }
  getYear() {
    return this.props.year
  }
  getContracts(): { contract: InvestmentContract; amount: number }[] {
    return this.props.contractYields
  }

  getTotalAmount() {
    return this.props.contractYields.reduce((acc, curr) => acc + curr.amount, 0)
  }

  getEmails(): IncomeReportEmail[] {
    return this.props.emails
  }

  getStatus(): IncomeReportStatus {
    return this.props.status
  }

  getInvestor(): Investor {
    return this.props.investor
  }

  getReferenceYear(): number {
    return this.props.year
  }
  getFileId(): string | null {
    return this.props.fileId ?? null
  }
}
