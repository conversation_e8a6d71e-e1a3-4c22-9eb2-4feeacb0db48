import { Either } from '../shared';

export interface IncomeReportPayload {
  amount: number;
  applicationBalance: number;
  calendarYear: string;
  employee: {
    name: string;
    document: string;
    bankAccount: {
      agency: string;
      account: string;
    };
  };
}

export interface IReportGateway {
  generateIncomeReport(
    data: IncomeReportPayload,
  ): Promise<Either<Error, Buffer>>;
}
