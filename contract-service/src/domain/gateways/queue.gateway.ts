export interface QueueJob<T = any> {
  id?: string
  data: T
  opts?: QueueJobOptions
}

export interface QueueJobOptions {
  priority?: number
  delay?: number
  attempts?: number
  backoff?: {
    type: string
    delay: number
  }
  removeOnComplete?: boolean | number
  removeOnFail?: boolean | number
}

export type JobTypeGateway =
  | 'completed'
  | 'waiting'
  | 'active'
  | 'delayed'
  | 'failed'

export interface IQueueGateway {
  add<T>(jobName: string, job: QueueJob<T>): Promise<void>
  getJobs<T>(status: JobTypeGateway): Promise<QueueJob<T>[]>
  removeJob(jobId: string): Promise<void>
}
