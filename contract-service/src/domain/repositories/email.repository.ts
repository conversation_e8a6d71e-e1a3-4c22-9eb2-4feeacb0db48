import { type Either } from '@/domain/shared';

export interface EmailData {
  id: string;
  from: string;
  to: string;
  body: string;
  bodyType: string;
  status: string;
  errorMessage?: string;
  externalId?: string;
}

export interface IEmailRepository {
  /**
   * Cria um novo registro de e-mail no banco de dados
   */
  create(emailData: EmailData): Promise<Either<Error, void>>;

  /**
   * Atualiza o external_id de um e-mail
   */
  updateExternalId(emailId: string, externalId: string): Promise<Either<Error, void>>;
}
