import type { Either } from "@/domain/shared";
import type { ITransactionContext } from "@/application/interfaces";
import type { ContractStatus } from "@/domain/entities/contracts";

export interface ContractDeletion {
  id: string;
  contractId: string;
  deletedById: string;
  reason: string;
  deletedAt: Date;
  updatedAt: Date;
}

export interface IContractDeletionRepository {
  create(
    data: Omit<ContractDeletion, "id" | "deletedAt" | "updatedAt">,
    tx?: ITransactionContext
  ): Promise<Either<Error, ContractDeletion>>;
}

export interface Contract {
  id: string;
  status: ContractStatus;
  updatedAt: Date;
  externalId: string;
}

export interface IContractRepository {
  findById(id: string): Promise<Either<Error, Contract | null>>;

  updateStatus(
    id: string,
    status: ContractStatus,
    tx?: ITransactionContext,
    externalId?: string
  ): Promise<Either<Error, Contract>>;
}
