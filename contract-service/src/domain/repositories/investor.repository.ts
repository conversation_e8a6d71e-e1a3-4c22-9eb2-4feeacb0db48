import type { ITransactionContext } from "@/application/interfaces";
import type { Investor } from "../entities/user";
import type { Either, Repository } from "../shared";
import type {
  owner,
  business,
  account,
  address,
  owner_business_relation,
} from "@prisma/client";

// Tipos usando os tipos nativos do Prisma
type OwnerWithAddressAndAccount = owner & {
  address: address[];
  account: account[];
};

type BusinessWithAddressAndAccount = business & {
  address: address[];
  account: account[];
  owner_business_relation: (owner_business_relation & {
    owner: owner & {
      address: address[];
    };
  })[];
};

type CreatedOwnerWithAddress = owner & {
  address: address[];
};

type CreatedBusinessWithAddress = business & {
  address: address[];
};

export interface IInvestorRepository extends Repository<Investor> {
  /**
   * Busca um investidor por documento (CPF ou CNPJ)
   * @param document - CPF ou CNPJ para buscar
   * @param tx - Contexto de transação opcional
   * @param pixFromDto - PIX key do DTO original (opcional)
   * @returns Either com Investor ou undefined se não encontrado
   */
  findByDocument: (
    document: string,
    tx?: ITransactionContext,
    pixFromDto?: string
  ) => Promise<Either<Error, Investor | undefined>>;

  /**
   * Busca um proprietário por CPF (sem verificar papel de investidor)
   * @param cpf - CPF para buscar
   * @param tx - Contexto de transação opcional
   * @returns Dados do proprietário ou null
   */
  findOwnerByCpf: (
    cpf: string,
    tx?: ITransactionContext
  ) => Promise<OwnerWithAddressAndAccount | null>;

  /**
   * Busca uma empresa por CNPJ
   * @param cnpj - CNPJ para buscar
   * @param tx - Contexto de transação opcional
   * @returns Dados da empresa ou null
   */
  findBusinessByCnpj: (
    cnpj: string,
    tx?: ITransactionContext
  ) => Promise<BusinessWithAddressAndAccount | null>;

  /**
   * Cria um novo registro de proprietário
   * @param data - Dados do proprietário para criar
   * @param tx - Contexto de transação opcional
   * @returns Dados do proprietário criado
   */
  createOwner: (
    data: {
      id: string;
      cpf: string;
      name: string;
      birthDate: Date;
      email: string;
      motherName: string;
      rg: string;
      occupation: string;
      phone: string;
      pep: string;
      address: {
        postalCode: string;
        city: string;
        neighborhood: string;
        number: string;
        state: string;
        street: string;
        complement?: string | null;
      };
    },
    tx?: ITransactionContext
  ) => Promise<CreatedOwnerWithAddress>;

  /**
   * Cria um novo registro de empresa
   * @param data - Dados da empresa para criar
   * @param tx - Contexto de transação opcional
   * @returns Dados da empresa criada
   */
  createBusiness: (
    data: {
      id: string;
      cnpj: string;
      size: string;
      companyName: string;
      email: string;
      ownerId: string;
      type: string;
      address: {
        postalCode: string;
        city: string;
        neighborhood: string;
        number: string;
        state: string;
        street: string;
        complement?: string;
      };
    },
    tx?: ITransactionContext
  ) => Promise<CreatedBusinessWithAddress>;

  /**
   * Atualiza dados pessoais de um proprietário (apenas campos vazios)
   * @param ownerId - ID do proprietário
   * @param data - Dados para atualizar
   * @param tx - Contexto de transação opcional
   */
  updateOwnerPersonalDataIfNeeded: (
    ownerId: string,
    data: {
      name?: string;
      email?: string;
      phone?: string;
      rg?: string;
      occupation?: string;
      issuingAgency?: string;
      nationality?: string;
      motherName?: string;
    },
    tx?: ITransactionContext
  ) => Promise<void>;

  /**
   * Cria um endereço para um proprietário
   * @param ownerId - ID do proprietário
   * @param addressData - Dados do endereço
   * @param tx - Contexto de transação opcional
   */
  createOwnerAddress: (
    ownerId: string,
    addressData: {
      postalCode: string;
      city: string;
      neighborhood: string;
      number: string;
      state: string;
      street: string;
      complement?: string;
    },
    tx?: ITransactionContext
  ) => Promise<void>;

  /**
   * Atualiza um endereço existente de um proprietário (apenas campos vazios)
   * @param ownerId - ID do proprietário
   * @param addressData - Dados do endereço para atualizar
   * @param tx - Contexto de transação opcional
   */
  updateOwnerAddressIfNeeded: (
    ownerId: string,
    addressData: {
      postalCode: string;
      city: string;
      neighborhood: string;
      number: string;
      state: string;
      street: string;
      complement?: string;
    },
    tx?: ITransactionContext
  ) => Promise<void>;

  /**
   * Cria uma conta bancária para um proprietário
   * @param ownerId - ID do proprietário
   * @param accountData - Dados da conta bancária
   * @param tx - Contexto de transação opcional
   */
  createOwnerAccount: (
    ownerId: string,
    accountData: {
      bank: string;
      number: string;
      branch: string;
      type: string;
    },
    tx?: ITransactionContext
  ) => Promise<void>;

  /**
   * Cria uma conta bancária para uma empresa
   * @param businessId - ID da empresa
   * @param accountData - Dados da conta bancária
   * @param tx - Contexto de transação opcional
   */
  createBusinessAccount: (
    businessId: string,
    accountData: {
      bank: string;
      number: string;
      branch: string;
      type: string;
    },
    tx?: ITransactionContext
  ) => Promise<void>;

  /**
   * Cria relação de papel de investidor
   * @param investorId - ID do investidor
   * @param ownerId - ID do proprietário (opcional)
   * @param businessId - ID da empresa (opcional)
   * @param tx - Contexto de transação opcional
   */
  createInvestorRoleRelation: (
    investorId: string,
    ownerId?: string,
    businessId?: string,
    tx?: ITransactionContext
  ) => Promise<void>;

  /**
   * Cria relação proprietário-empresa
   * @param businessId - ID da empresa
   * @param ownerId - ID do proprietário
   * @param tx - Contexto de transação opcional
   */
  createOwnerBusinessRelation: (
    businessId: string,
    ownerId: string,
    tx?: ITransactionContext
  ) => Promise<void>;

  /**
   * Verifica se uma relação owner_business_relation já existe
   * @param businessId - ID da empresa
   * @param ownerId - ID do proprietário
   * @param tx - Contexto de transação opcional
   * @returns true se existe, false caso contrário
   */
  hasOwnerBusinessRelation: (
    businessId: string,
    ownerId: string,
    tx?: ITransactionContext
  ) => Promise<boolean>;

  /**
   * Atualiza a senha do investidor
   * @param investorId - ID do investidor
   * @param password - Nova senha
   * @returns Either com void ou Error
   */
  updatePassword: (
    investorId: string,
    password: string
  ) => Promise<Either<Error, void>>;

  /**
   * Verifica se uma parte tem outros papéis além do atual
   * @param relationId - ID da relação
   * @returns true se tem outros papéis, false caso contrário
   */
  hasOtherRolesForParty: (relationId: string) => Promise<boolean>;

  /**
   * Obtém os IDs do proprietário e empresa por ID da relação
   * @param relationId - ID da relação
   * @returns Objeto com ownerId e businessId (um será null)
   */
  getPartyIdsByRelationId: (relationId: string) => Promise<{
    ownerId: string | null;
    businessId: string | null;
  }>;

  /**
   * Verifica se um proprietário já possui conta bancária
   * @param ownerId - ID do proprietário
   * @param tx - Contexto de transação opcional
   * @returns true se possui conta, false caso contrário
   */
  hasOwnerAccount: (
    ownerId: string,
    tx?: ITransactionContext
  ) => Promise<boolean>;

  /**
   * Verifica se uma empresa já possui conta bancária
   * @param businessId - ID da empresa
   * @param tx - Contexto de transação opcional
   * @returns true se possui conta, false caso contrário
   */
  hasBusinessAccount: (
    businessId: string,
    tx?: ITransactionContext
  ) => Promise<boolean>;

  /**
   * Atualiza uma conta bancária de proprietário (apenas campos vazios)
   */
  updateOwnerAccountIfNeeded: (
    ownerId: string,
    accountData: {
      bank: string;
      number: string;
      branch: string;
      type: string;
    },
    tx?: ITransactionContext
  ) => Promise<void>;

  /**
   * Atualiza uma conta bancária de empresa (apenas campos vazios)
   */
  updateBusinessAccountIfNeeded: (
    businessId: string,
    accountData: {
      bank: string;
      number: string;
      branch: string;
      type: string;
    },
    tx?: ITransactionContext
  ) => Promise<void>;

  /**
   * Atualiza dados de uma empresa existente se necessário
   */
  updateBusinessIfNeeded: (
    businessId: string,
    data: {
      companyName: string;
      email: string;
    },
    tx?: ITransactionContext
  ) => Promise<void>;
}
