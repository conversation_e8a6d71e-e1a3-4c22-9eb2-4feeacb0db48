import type { CreateNewContractDTO } from '@/application/dtos/contracts/create-new-contract.dto'
import type { CreateNewContractUseCase } from '@/application/usecases/contracts/create-new-contract.usecase'
import type { InvestmentContract } from '@/domain/entities/contracts'
import { badRequest, ok, serverError } from '../../helpers/http-helper'
import type { HttpRequest, HttpResponse, IController } from '../../protocols'
import type { IValidator } from '../../validation/validator'

export class CreateNewContractController implements IController {
  constructor(
    private readonly useCase: CreateNewContractUseCase,
    private readonly validator: IValidator<CreateNewContractDTO>
  ) {}

  async handle(
    request: HttpRequest
  ): Promise<HttpResponse | HttpResponse<InvestmentContract>> {
    const validationResult = this.validator.validate(request.body)

    if (validationResult.isLeft()) {
      const error = validationResult.value
      return badRequest({ error: error.name, message: error.errors })
    }

    const contract = await this.useCase.execute(validationResult.value)

    if (contract.isLeft()) {
      return serverError(contract.value)
    }

    return ok(contract.value)
  }
}
