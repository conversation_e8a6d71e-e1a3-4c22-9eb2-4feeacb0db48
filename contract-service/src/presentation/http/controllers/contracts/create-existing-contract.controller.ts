import type { CreateExistingContractDTO } from '@/application/dtos/contracts/create-existing-contract.dto'
import type { CreateExistingContractUseCase } from '@/application/usecases/contracts/create-existing-contract.usecase'
import type { ContractFiles } from '@/domain/interfaces/file-upload'
import { badRequest, ok, serverError } from '../../helpers/http-helper'
import type { HttpRequest, HttpResponse, IController } from '../../protocols'
import type { IValidator } from '../../validation/validator'

interface CreateExistingContractRequest extends HttpRequest {
  body: {
    personType: string
    contractType: string
    brokerId: string
    investment: {
      amount: number
      monthlyRate: number
      paymentMethod: string
      profile: string
      startDate: string
      endDate: string
      quotaQuantity?: number | string
    }
    advisors?: Array<{
      id: string
      role: string
    }>
    bankAccount: {
      bank: string
      agency: string
      account: string
      accountType: string
      accountHolder: string
      accountHolderDocument: string
    }
    individual?: {
      name: string
      document: string
      email: string
      phone: string
      address: {
        street: string
        number: string
        city: string
        state: string
        postalCode: string
        neighborhood: string
        complement?: string
      }
    }
    company?: {
      name: string
      document: string
      email: string
      phone: string
      legalType: string
      address: {
        street: string
        number: string
        city: string
        state: string
        postalCode: string
        neighborhood: string
        complement?: string
      }
      representative: {
        name: string
        document: string
        email: string
        phone: string
        address: {
          street: string
          number: string
          city: string
          state: string
          postalCode: string
          neighborhood: string
          complement?: string
        }
      }
    }
  } & ContractFiles
}

export class CreateExistingContractController implements IController {
  constructor(
    private readonly useCase: CreateExistingContractUseCase,
    private readonly validator: IValidator<CreateExistingContractDTO>
  ) {}

  async handle(request: CreateExistingContractRequest): Promise<HttpResponse> {
    try {
      const data = request.body

      

      if (!data.proofOfPayment || !data.personalDocument || !data.contract) {
        return badRequest({
          error: 'ValidationError',
          message: 'Todos os arquivos são obrigatórios',
        })
      }

      if (data.investment?.quotaQuantity && typeof data.investment.quotaQuantity === 'string') {
        data.investment.quotaQuantity = Number(data.investment.quotaQuantity);
      }

      
      
      const contractData = {
        ...data,
        proofOfPayment: {
          mimetype: data.proofOfPayment.mimetype,
          buffer: data.proofOfPayment.buffer,
        },
        personalDocument: {
          mimetype: data.personalDocument.mimetype,
          buffer: data.personalDocument.buffer,
        },
        contract: {
          mimetype: data.contract.mimetype,
          buffer: data.contract.buffer,
        },
      }

      
      
      const validationResult = this.validator.validate(contractData)

      

      if (validationResult.isLeft()) {
        const error = validationResult.value
        return badRequest({ error: error.name, message: error.errors })
      }

      const result = await this.useCase.execute(validationResult.value)

      if (result.isLeft()) {
        return serverError(result.value)
      }

      return ok(result.value)
    } catch (error) {
      console.error('Error processing request:', error)
      return serverError(error as Error)
    }
  }
}
