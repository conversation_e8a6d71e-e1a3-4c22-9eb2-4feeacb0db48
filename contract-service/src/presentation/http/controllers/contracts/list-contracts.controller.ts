import type { ListContractsUseCase } from '@/application/usecases/contracts/list-contracts.usecase'
import { badRequest, ok } from '../../helpers/http-helper'
import type { HttpRequest, IController } from '../../protocols'

export class ListContractsController implements IController {
  constructor(private useCase: ListContractsUseCase) {}

  async handle(request: HttpRequest<{ userId: string }>) {
    const userId: string | undefined = request.query?.userId ?? undefined
    const page: string | undefined = request.query?.page ?? undefined
    const limit: string | undefined = request.query?.limit ?? undefined

    if (!userId) {
      return badRequest(new Error(''))
    }

    if (page && Number.isNaN(page)) {
      return badRequest(new Error(''))
    }

    if (limit && Number.isNaN(limit)) {
      return badRequest(new Error(''))
    }

    const response = await this.useCase.execute({
      userId: userId,
      page: page ? Number(page) : undefined,
      limit: limit ? Number(limit) : undefined,
    })

    if (response.isLeft()) {
      return badRequest(new Error('teste'))
    }

    return ok({
      ...response.value,
      items: response.value.items.map(contract => {
        const investor = contract.getInvestor()
        const investorData = {
          id: investor.id,
          name: investor.getName(),
          email: investor.getEmail(),
          address: investor.getParty().getAddress(),
          cnpj: investor.getCnpj(),
          cpf: investor.getParty().getFormattedCpf(),
        }

        const broker = contract.getBroker()
        const brokerData = {
          id: broker,
        }

        const advisors = []
        for (const advisor of contract.getAdvisors()) {
          advisors.push({
            id: advisor.id,
          })
        }
        return {
          id: contract.id,
          status: contract.getStatus(),
          investmentValue: contract.getAmount().formatBRL(),
          investor: investorData,
          broker: brokerData,
          advisors: advisors,
        }
      }),
    })
  }
}
