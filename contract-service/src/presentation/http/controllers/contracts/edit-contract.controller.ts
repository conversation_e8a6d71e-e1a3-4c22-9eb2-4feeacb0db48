import type { EditContractDTO } from "@/application/dtos/contracts/edit-contract.dto";
import { badRequest, ok } from "../../helpers/http-helper";
import type { HttpRequest, HttpResponse, IController } from "../../protocols";
import type { IValidator } from "../../validation/validator";
import type { IUseCase } from "@/application/interfaces/usecase";
import { Either } from "@/domain/shared";

export class EditContractController implements IController {
  constructor(
    private readonly validator: IValidator<EditContractDTO>,
    private readonly useCase: IUseCase<
      EditContractDTO,
      Promise<Either<Error, void>>
    >
  ) {}

  async handle(request: HttpRequest): Promise<HttpResponse> {
    if (!request.params?.id) {
      return badRequest({
        error: "ValidationError",
        message: ["ID do contrato é obrigatório"],
      });
    }

    const validationData = {
      ...request.body,
      contractId: request.params.id,
    };

    const validationResult = this.validator.validate(validationData);

    if (validationResult.isLeft()) {
      const error = validationResult.value;
      return badRequest({ error: error.name, message: error.errors });
    }

    const result = await this.useCase.execute(validationResult.value);

    if (result.isLeft()) {
      return badRequest(result.value);
    }

    return ok(result.value);
  }
}
