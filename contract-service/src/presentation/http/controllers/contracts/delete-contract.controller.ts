import type { HttpRequest, HttpResponse } from "@/presentation/http/protocols";
import type { IController } from "@/presentation/http/protocols/controller";
import type { IValidator } from "../../validation/validator";
import type { IUseCase } from "@/application/interfaces/usecase";
import { Either } from "@/domain/shared";
import { badRequest, ok } from "../../helpers/http-helper";
import { DeleteContractDTO } from "@/application/dtos/contracts/deleted-contract.dto";

interface DeleteContractResponse {
  id: string;
}

export class DeleteContractController implements IController {
  constructor(
    private readonly validator: IValidator<DeleteContractDTO>,
    private readonly useCase: IUseCase<
      DeleteContractDTO,
      Promise<Either<Error, DeleteContractResponse>>
    >
  ) {}

  async handle(request: HttpRequest): Promise<HttpResponse> {
    const contractId = request.params?.contractId;
    if (!contractId) {
      return badRequest({
        error: "ValidationError",
        message: ["O ID do contrato é obrigatório"],
      });
    }

    const validationResult = this.validator.validate({
      contractId,
      role: request.body?.role,
      reason: request.body?.reason,
      userId: request.body?.userId,
    });

    if (validationResult.isLeft()) {
      const error = validationResult.value;
      return badRequest({ error: error.name, message: error.errors });
    }

    const result = await this.useCase.execute(validationResult.value);

    if (result.isLeft()) {
      return badRequest(result.value);
    }

    return ok(result.value);
  }
}
