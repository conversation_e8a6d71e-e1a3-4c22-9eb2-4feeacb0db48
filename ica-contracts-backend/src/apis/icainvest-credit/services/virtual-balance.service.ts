import { Injectable } from '@nestjs/common';
import axios from 'axios';
import { logger } from 'src/shared/logger';

import { IVirtualBalanceRequest } from '../requests/virtual-balance.request';
import { IVirtualBalanceResponse } from '../responses/virtual-balance.response';

@Injectable()
export class VirtualBalanceService {
  async checkBalance(
    params: IVirtualBalanceRequest,
  ): Promise<IVirtualBalanceResponse> {
    try {
      const URL = `https://invest-credit.icabankteam.com/balance/${params.accountId}`;

      const response = await axios.get<IVirtualBalanceResponse>(URL, {
        headers: {
          'X-Api-Key': 'c9e0d900-edd3-45f7-9e69-abd96edbf724',
        },
      });

      return response.data;
    } catch (error) {
      logger.info(`VirtualBalanceService.checkBalance() -> ${error}`);
      return { amount: 0 };
    }
  }
}
