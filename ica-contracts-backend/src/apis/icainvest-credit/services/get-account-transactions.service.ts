import { Injectable, HttpException } from '@nestjs/common';
import axios, { AxiosResponse } from 'axios';

import { IGetAccountTransactionsRequest } from '../requests/get-account-transactions.request';
import { IGetAccountTransactionsResponse } from '../responses/get-account-transactions.response';

@Injectable()
export class GetAccountTransactionsService {
  async perform(
    transaction: IGetAccountTransactionsRequest,
  ): Promise<IGetAccountTransactionsResponse[]> {
    try {
      const url = `${process.env.TRANSACTIONS_URL}/transactions/${transaction.id}`;

      const { data }: AxiosResponse<IGetAccountTransactionsResponse[]> =
        await axios.get(url, {
          headers: {
            'X-Api-Key': 'c9e0d900-edd3-45f7-9e69-abd96edbf724',
            'Content-Type': 'application/json',
          },
        });

      return data;
    } catch (error) {
      console.log(error);

      throw new HttpException(
        error.response.data.message,
        error.response.data.statusCode,
      );
    }
  }
}
