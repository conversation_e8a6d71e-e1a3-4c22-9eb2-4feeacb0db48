export interface IUpdatedBusinessApiRequest {
  businessEmail?: string;
  contactNumber?: string;
  businessAddress?: {
    postalCode?: string;
    street?: string;
    number?: string;
    addressComplement?: string;
    neighborhood?: string;
    city?: string;
    state?: string;
  };
  owners?: IOwner[];
  Account?: string;
  DocumentNumber?: string;
}

export interface IOwner {
  documentNumber: string;
  address?: {
    postalCode?: string;
    street?: string;
    number?: string;
    addressComplement?: string;
    neighborhood?: string;
    city?: string;
    state?: string;
  };
  isPoliticallyExposedPerson?: boolean;
}
