export class CreatePixQrcodeDynamicCelcointRequest {
  clientRequestId: string;
  key: string;
  amount: string;
  merchant: {
    postalCode: string;
    city: string;
    merchantCategoryCode: string;
    name: string;
  };
  payerName?: string;
  payerCPF?: string;
  payerCNPJ?: string;
  payerQuestion?: string;
  expiration?: number;
  additionalInformation?: Array<{
    value: string;
    key: string;
  }>;
}
