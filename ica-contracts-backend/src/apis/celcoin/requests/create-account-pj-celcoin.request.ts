interface IAddress {
  postalCode: string;
  street: string;
  number: string;
  addressComplement: string;
  neighborhood: string;
  city: string;
  state: string;
  longitude: string;
  latitude: string;
}

interface IOwner {
  documentNumber: string;
  fullName: string;
  phoneNumber: string;
  email: string;
  motherName: string;
  socialName: string;
  birthDate: string;
  address: Omit<IAddress, 'longitude' | 'latitude'>;
  isPoliticallyExposedPerson: boolean;
}

export interface ICreateAccountPJCelcoinRequest {
  clientCode: string;
  accountOnboardingType: string;
  documentNumber: string;
  contactNumber: string;
  businessEmail: string;
  businessName: string;
  tradingName: string;
  owner: Array<IOwner>;
  businessAddress: IAddress;
  cadastraChavePix: boolean;
}
