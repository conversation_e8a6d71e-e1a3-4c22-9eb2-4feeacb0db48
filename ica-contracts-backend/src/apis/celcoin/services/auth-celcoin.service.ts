import { CACHE_MANAGER } from '@nestjs/cache-manager';
import {
  Inject,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import * as AWS from 'aws-sdk';
import axios, { AxiosResponse } from 'axios';
import { Cache } from 'cache-manager';
import * as https from 'https';
import { decode } from 'jsonwebtoken';
import { IDecodeJwt } from 'src/shared/interfaces/decode-jwt.interface';

@Injectable()
export class AuthCelcoinService {
  constructor(@Inject(CACHE_MANAGER) private cacheManager: Cache) {}
  async getToken() {
    const token: string = await this.cacheManager.get('celcoin_token');
    const today = new Date();
    if (token) {
      const decodeJwt = decode(token) as IDecodeJwt;
      if (today.getTime() > decodeJwt.exp) {
        const genToken = await this.genToken();
        return { accessToken: genToken };
      }
    }

    const genToken = await this.genToken();
    return { accessToken: genToken };
  }

  private async genToken() {
    const s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });
    const cert = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_certificado.crt',
      })
      .promise();

    const key = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_chave_decrypted.key',
      })
      .promise();

    const httpsAgent = new https.Agent({
      cert: cert.Body as any,
      key: key.Body as any,
    });

    const form = new FormData();
    form.append('client_id', process.env.CELCOIN_ID);
    form.append('client_secret', process.env.CELCOIN_SECRET);
    form.append('grant_type', 'client_credentials');

    const {
      data: { access_token: accessToken },
    }: AxiosResponse<{ access_token: string }> = await axios
      .post(`${process.env.CELCOIN_URL}/v5/token`, form, { httpsAgent })
      .catch((error) => {
        throw new InternalServerErrorException(
          `Erro ao gerar o token ${error.response.data}`,
        );
      });
    await this.cacheManager.set('celcoin_token', accessToken);
    return accessToken;
  }
}
