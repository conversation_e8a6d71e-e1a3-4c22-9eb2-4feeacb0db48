import { Inject, Injectable } from '@nestjs/common';
import * as AWS from 'aws-sdk';
import axios, { AxiosResponse } from 'axios';
import * as https from 'https';

import { IGenerateCelcoinBoletoRequest } from '../requests/generate-celcoin-boleto.request';
import { IGenerateCelcoinBoletoResponse } from '../responses/generate-celcoin-boleto.response';
import { AuthCelcoinService } from './auth-celcoin.service';

@Injectable()
export class GenerateCelcoinBoletoService {
  constructor(
    @Inject(AuthCelcoinService)
    private authCelcoinService: AuthCelcoinService,
  ) {}

  async perform(input: IGenerateCelcoinBoletoRequest) {
    const transactionId = await this.generateBoleto(input);

    return { transactionId };
  }

  private async generateBoleto(payload: IGenerateCelcoinBoletoRequest) {
    const { accessToken: token } = await this.authCelcoinService.getToken();
    const s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });
    const cert = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_certificado.crt',
      })
      .promise();

    const key = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_chave_decrypted.key',
      })
      .promise();

    const httpsAgent = new https.Agent({
      cert: cert.Body as any,
      key: key.Body as any,
    });

    const {
      data: {
        body: { transactionId },
      },
    }: AxiosResponse<IGenerateCelcoinBoletoResponse> = await axios.post(
      `${process.env.CELCOIN_URL}/api-integration-baas-webservice/v1/charge`,
      payload,
      { headers: { Authorization: `Bearer ${token}` }, httpsAgent },
    );

    return transactionId;
  }
}
