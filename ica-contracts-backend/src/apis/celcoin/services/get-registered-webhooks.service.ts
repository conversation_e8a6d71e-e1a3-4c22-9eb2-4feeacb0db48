import {
  Injectable,
  Inject,
  InternalServerErrorException,
} from '@nestjs/common';
import * as AWS from 'aws-sdk';
import axios from 'axios';
import * as https from 'https';

import { IConsultCelcoinWebhookRequest } from '../requests/consult-webhook-celcoin.request';
import { AuthCelcoinService } from './auth-celcoin.service';

@Injectable()
export class GetRegisteredWebhooksService {
  constructor(
    @Inject(AuthCelcoinService)
    private authCelcoinService: AuthCelcoinService,
  ) {}

  async perform(input: IConsultCelcoinWebhookRequest) {
    const token = await this.authCelcoinService.getToken();
    const s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });
    const cert = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_certificado.crt',
      })
      .promise();

    const key = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_chave_decrypted.key',
      })
      .promise();

    const httpsAgent = new https.Agent({
      cert: cert.Body as any,
      key: key.Body as any,
    });
    const url = `${process.env.CELCOIN_URL}/baas-webhookmanager/v1/webhook/subscription?entity=${input.entity}&active=${input.active}`;
    const config = {
      headers: {
        Authorization: `Bearer ${token.accessToken}`,
      },
      httpsAgent,
    };

    try {
      const { data } = await axios.get(url, config);

      return data;
    } catch (error) {
      throw new InternalServerErrorException(error.response.data);
    }
  }
}
