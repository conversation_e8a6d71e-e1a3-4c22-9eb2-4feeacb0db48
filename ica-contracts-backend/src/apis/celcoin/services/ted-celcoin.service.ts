import {
  Inject,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import * as AWS from 'aws-sdk';
import axios, { AxiosResponse } from 'axios';
import * as https from 'https';

import { IGetTedCelcoinRequest } from '../requests/get-ted-celcoin.request';
import { ISendTedCelcoinRequest } from '../requests/send-ted-celcoin.request';
import { IGetTedCelcoinResponse } from '../responses/get-ted-celcoin.response';
import { ISendTedCelcoinResponse } from '../responses/send-ted-celcoin.response';
import { AuthCelcoinService } from './auth-celcoin.service';

@Injectable()
export class TedCelcoinService {
  constructor(
    @Inject(AuthCelcoinService)
    private authCelcoinService: AuthCelcoinService,
  ) {}

  async sendTed(
    input: ISendTedCelcoinRequest,
  ): Promise<ISendTedCelcoinResponse> {
    const token = await this.authCelcoinService.getToken();
    const s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });
    const cert = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_certificado.crt',
      })
      .promise();

    const key = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_chave_decrypted.key',
      })
      .promise();

    const httpsAgent = new https.Agent({
      cert: cert.Body as any,
      key: key.Body as any,
    });

    const url = `${process.env.CELCOIN_URL}/baas-wallet-transactions-webservice/v1/spb/transfer`;

    const config = {
      headers: {
        Authorization: `Bearer ${token.accessToken}`,
      },
      httpsAgent,
    };

    try {
      const { data }: AxiosResponse<ISendTedCelcoinResponse> = await axios.post(
        url,
        input,
        config,
      );
      return data;
    } catch (error) {
      if (
        error.response.data?.error?.errorCode &&
        error.response.data.error.errorCode === 'CBE123'
      ) {
        throw new InternalServerErrorException(
          'Estamos passando por uma manutenção e atualização do nosso sistema.',
        );
      }
      throw new InternalServerErrorException(error.response.data);
    }
  }

  async getTed(input: IGetTedCelcoinRequest) {
    const token = await this.authCelcoinService.getToken();
    const s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });
    const cert = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_certificado.crt',
      })
      .promise();

    const key = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_chave_decrypted.key',
      })
      .promise();

    const httpsAgent = new https.Agent({
      cert: cert.Body as any,
      key: key.Body as any,
    });

    const url = `${process.env.CELCOIN_URL}/baas-wallet-transactions-webservice/v1/spb/transfer/status`;

    const config = {
      headers: {
        Authorization: `Bearer ${token.accessToken}`,
      },
      params: {
        id: input.id,
      },
      httpsAgent,
    };

    try {
      const { data }: AxiosResponse<IGetTedCelcoinResponse> = await axios.get(
        url,
        config,
      );
      return data;
    } catch (error) {
      throw new InternalServerErrorException(error.response.data);
    }
  }
}
