import {
  Inject,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import * as AWS from 'aws-sdk';
import axios, { AxiosResponse } from 'axios';
import * as https from 'https';
import { logger } from 'src/shared/logger';

import { IGetAccountExternalPixKeyCelcoinRequest } from '../requests/get-account-external-pix-key-celcoin.request';
import { IGetInternalPixTransaction } from '../requests/get-internal-pix-transaction-celcoin.request';
import { IGetPixParticipantsCelcoinRequest } from '../requests/get-pix-participants-celcoin.request';
import { IGetPixTransactionStatusCelcoinRequest } from '../requests/get-pix-transaction-status-celcoin.request';
import { IInternalTransferRequest } from '../requests/internal-transaction-celcoin.request';
import { IPixReturnCelcoinRequest } from '../requests/pix-return-celcoin.request';
import { IReversePixCelcoinRequest } from '../requests/reverse-pix-celcoin.request';
import { ITransactionPixCelcoinRequest } from '../requests/transaction-pix-celcoin.request';
import { IGetAccountExternalPixKeyCelcoinResponse } from '../responses/get-account-external-pix-key-celcoin.response';
import { IGetInternalPixTransactionCelcoinResponse } from '../responses/get-internal-pix-transaction-celcoin.response';
import { IGetPixParticipantsResponse } from '../responses/get-pix-participants-celcoin.response';
import { IGetPixTransactionStatusCelcoinResponse } from '../responses/get-pix-transaction-status-celcoin.response';
import { IInternalTransferResponse } from '../responses/internal-transaction-celcoin.response';
import { IPixReturnCelcoinResponse } from '../responses/pix-return-celcoin.response';
import { IReversePixCelcoinResponse } from '../responses/reverse-pix-celcoin.response';
import { ITransactionPixCelcoinResponse } from '../responses/transaction-pix-celcoin.response';
import { AuthCelcoinService } from './auth-celcoin.service';

@Injectable()
export class PixTransactionCelcoinService {
  constructor(
    @Inject(AuthCelcoinService)
    private authCelcoinService: AuthCelcoinService,
  ) {}

  async getAccountExternalPixKey(
    input: IGetAccountExternalPixKeyCelcoinRequest,
  ) {
    const token = await this.authCelcoinService.getToken();
    const s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });
    const cert = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_certificado.crt',
      })
      .promise();

    const key = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_chave_decrypted.key',
      })
      .promise();

    const httpsAgent = new https.Agent({
      cert: cert.Body as any,
      key: key.Body as any,
    });

    const url = `${process.env.CELCOIN_URL}/celcoin-baas-pix-dict-webservice/v1/pix/dict/entry/external/${input.accountNumber}`;
    const config = {
      headers: {
        Authorization: `Bearer ${token.accessToken}`,
      },
      params: {
        key: input.key,
      },
      httpsAgent,
    };

    try {
      const { data }: AxiosResponse<IGetAccountExternalPixKeyCelcoinResponse> =
        await axios.get(url, config);
      return data;
    } catch (error) {
      logger.error(
        `PixTransactionCelcoinService.getAccountExternalPixKey() -> ${error.response.data}`,
        error.response.data,
      );
      throw new InternalServerErrorException(error.response.data);
    }
  }

  async transactionPixKey(input: ITransactionPixCelcoinRequest) {
    const token = await this.authCelcoinService.getToken();
    const s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });
    const cert = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_certificado.crt',
      })
      .promise();

    const key = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_chave_decrypted.key',
      })
      .promise();

    const httpsAgent = new https.Agent({
      cert: cert.Body as any,
      key: key.Body as any,
    });

    const url = `${process.env.CELCOIN_URL}/baas-wallet-transactions-webservice/v1/pix/payment`;
    const config = {
      headers: {
        Authorization: `Bearer ${token.accessToken}`,
      },
      httpsAgent,
    };

    try {
      const { data }: AxiosResponse<ITransactionPixCelcoinResponse> =
        await axios.post(url, input, config);
      return data;
    } catch (error) {
      logger.error(
        `PixTransactionCelcoinService.transactionPixKey() -> ${error}`,
        error,
      );

      console.log(error);

      if (
        error.response.data?.error?.errorCode &&
        error.response.data.error.errorCode === 'CBE123'
      ) {
        throw new InternalServerErrorException(
          'Estamos passando por uma manutenção e atualização do nosso sistema.',
        );
      }

      throw new InternalServerErrorException(error.response.data);
    }
  }

  async getTransactionPixStatus(
    input: IGetPixTransactionStatusCelcoinRequest,
  ): Promise<IGetPixTransactionStatusCelcoinResponse> {
    const token = await this.authCelcoinService.getToken();
    const s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });
    const cert = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_certificado.crt',
      })
      .promise();

    const key = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_chave_decrypted.key',
      })
      .promise();

    const httpsAgent = new https.Agent({
      cert: cert.Body as any,
      key: key.Body as any,
    });

    const url = `${process.env.CELCOIN_URL}/baas-wallet-transactions-webservice/v1/pix/payment/status`;

    const config = {
      headers: {
        Authorization: `Bearer ${token.accessToken}`,
      },
      httpsAgent,
      params: {
        ...input,
      },
    };

    try {
      const { data }: AxiosResponse<IGetPixTransactionStatusCelcoinResponse> =
        await axios.get(url, config);
      return data;
    } catch (error) {
      logger.error(
        `PixTransactionCelcoinService.getTransactionPixStatus() -> ${error.response.data}`,
        error.response.data,
      );
      throw new InternalServerErrorException(error.response.data);
    }
  }

  async getPixParticipants(
    input: IGetPixParticipantsCelcoinRequest,
  ): Promise<IGetPixParticipantsResponse> {
    const token = await this.authCelcoinService.getToken();
    const s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });
    const cert = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_certificado.crt',
      })
      .promise();

    const key = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_chave_decrypted.key',
      })
      .promise();

    const httpsAgent = new https.Agent({
      cert: cert.Body as any,
      key: key.Body as any,
    });

    const url = `${process.env.CELCOIN_URL}/baas-wallet-transactions-webservice/v1/pix/participant${new URLSearchParams(
      {
        ...input,
      },
    )}`;

    const config = {
      headers: {
        Authorization: `Bearer ${token.accessToken}`,
      },
      httpsAgent,
    };

    try {
      const { data }: AxiosResponse<IGetPixParticipantsResponse> =
        await axios.get(url, config);
      return data;
    } catch (error) {
      logger.error(
        'PixTransactionCelcoinService.getPixParticipants() -> ',
        error,
      );
      throw new InternalServerErrorException(error.response.data);
    }
  }

  async getInternalPixTransaction(
    input: IGetInternalPixTransaction,
  ): Promise<IGetInternalPixTransactionCelcoinResponse> {
    const token = await this.authCelcoinService.getToken();
    const s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });
    const cert = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_certificado.crt',
      })
      .promise();

    const key = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_chave_decrypted.key',
      })
      .promise();

    const httpsAgent = new https.Agent({
      cert: cert.Body as any,
      key: key.Body as any,
    });

    const url = `${process.env.CELCOIN_URL}/baas-wallet-transactions-webservice/v1/wallet/internal/transfer/status?${new URLSearchParams(
      {
        ...input,
      },
    )}`;

    const config = {
      headers: {
        Authorization: `Bearer ${token.accessToken}`,
      },
      httpsAgent,
    };

    try {
      const { data }: AxiosResponse<IGetInternalPixTransactionCelcoinResponse> =
        await axios.get(url, config);
      return data;
    } catch (error) {
      logger.error(
        'PixTransactionCelcoinService.getInternalPixTransaction() -> ',
        error,
      );
      if (error.response?.data?.error?.message)
        throw new NotFoundException(error.response.data.error.message);

      throw new InternalServerErrorException(error.response.data);
    }
  }

  async internalTransfer(
    requestData: IInternalTransferRequest,
  ): Promise<IInternalTransferResponse> {
    try {
      const token = await this.authCelcoinService.getToken();
      const s3 = new AWS.S3({
        accessKeyId: process.env.AWS_ACCESS_KEY,
        secretAccessKey: process.env.AWS_SECRET_KEY,
        region: process.env.AWS_REGION,
      });
      const cert = await s3
        .getObject({
          Bucket: 'keys-icabank',
          Key: 'newinvest_certificado.crt',
        })
        .promise();

      const key = await s3
        .getObject({
          Bucket: 'keys-icabank',
          Key: 'newinvest_chave_decrypted.key',
        })
        .promise();

      const httpsAgent = new https.Agent({
        cert: cert.Body as any,
        key: key.Body as any,
      });

      const response = await axios.post<IInternalTransferResponse>(
        `${process.env.CELCOIN_URL}/baas-wallet-transactions-webservice/v1/wallet/internal/transfer`,
        requestData,
        {
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token.accessToken}`,
          },
          httpsAgent,
        },
      );
      return response.data;
    } catch (error) {
      logger.error(
        'PixTransactionCelcoinService.internalTransfer() -> ',
        error.response,
      );

      throw new InternalServerErrorException(error.response.data);
    }
  }

  async reverse(
    input: IReversePixCelcoinRequest,
  ): Promise<IReversePixCelcoinResponse> {
    const token = await this.authCelcoinService.getToken();
    const s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });
    const cert = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_certificado.crt',
      })
      .promise();

    const key = await s3
      .getObject({
        Bucket: 'keys-icabank',
        Key: 'newinvest_chave_decrypted.key',
      })
      .promise();

    const httpsAgent = new https.Agent({
      cert: cert.Body as any,
      key: key.Body as any,
    });

    const url = `${process.env.CELCOIN_URL}/baas-wallet-transactions-webservice/v1/pix/reserve`;

    const config = {
      headers: {
        Authorization: `Bearer ${token.accessToken}`,
      },
      httpsAgent,
    };

    try {
      const { data }: AxiosResponse<IReversePixCelcoinResponse> =
        await axios.post(url, input, config);
      return data;
    } catch (error) {
      logger.error('PixTransactionCelcoinService.reverse() -> ', error);
      throw new InternalServerErrorException(error.response.data);
    }
  }

  async devolution(
    input: IPixReturnCelcoinRequest,
  ): Promise<IPixReturnCelcoinResponse> {
    try {
      const token = await this.authCelcoinService.getToken();
      const s3 = new AWS.S3({
        accessKeyId: process.env.AWS_ACCESS_KEY,
        secretAccessKey: process.env.AWS_SECRET_KEY,
        region: process.env.AWS_REGION,
      });
      const cert = await s3
        .getObject({
          Bucket: 'keys-icabank',
          Key: 'newinvest_certificado.crt',
        })
        .promise();

      const key = await s3
        .getObject({
          Bucket: 'keys-icabank',
          Key: 'newinvest_chave_decrypted.key',
        })
        .promise();

      const httpsAgent = new https.Agent({
        cert: cert.Body as any,
        key: key.Body as any,
      });

      const response = await axios.post<IPixReturnCelcoinResponse>(
        `${process.env.CELCOIN_URL}/baas-wallet-transactions-webservice/v1/pix/reverse`,
        input,
        {
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token.accessToken}`,
          },
          httpsAgent,
        },
      );
      return response.data;
    } catch (error) {
      logger.error(
        'PixTransactionCelcoinService.devolution() -> ',
        error.response,
      );
      throw new InternalServerErrorException(error.response.data);
    }
  }
}
