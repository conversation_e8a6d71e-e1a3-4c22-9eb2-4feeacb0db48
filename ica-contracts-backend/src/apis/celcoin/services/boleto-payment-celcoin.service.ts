import { Injectable, InternalServerErrorException } from '@nestjs/common';
import * as AWS from 'aws-sdk';
import axios, { isAxiosError } from 'axios';
import * as https from 'https';
import { logger } from 'src/shared/logger';

import { IBoletoPaymentCelcoin } from '../requests/boleto-payment-celcoin.request';
import { AuthCelcoinService } from './auth-celcoin.service';

@Injectable()
export class BoletoPaymentCelcoinService {
  constructor(private authCelcoinService: AuthCelcoinService) {}

  async perform(input: IBoletoPaymentCelcoin) {
    try {
      const { accessToken: token } = await this.authCelcoinService.getToken();
      const s3 = new AWS.S3({
        accessKeyId: process.env.AWS_ACCESS_KEY,
        secretAccessKey: process.env.AWS_SECRET_KEY,
        region: process.env.AWS_REGION,
      });
      const cert = await s3
        .getObject({
          Bucket: 'keys-icabank',
          Key: 'newinvest_certificado.crt',
        })
        .promise();

      const key = await s3
        .getObject({
          Bucket: 'keys-icabank',
          Key: 'newinvest_chave_decrypted.key',
        })
        .promise();

      const httpsAgent = new https.Agent({
        cert: cert.Body as any,
        key: key.Body as any,
      });
      const { data } = await axios.post(
        `${process.env.CELCOIN_URL}/baas/v2/billpayment`,
        {
          clientRequestId: input.clientRequestId,
          amount: input.amount,
          account: input.account,
          transactionIdAuthorize: input.transactionIdAuthorize,
          barCodeInfo: {
            barCode: input.barCode,
            digitable: input.digitable,
          },
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
          httpsAgent,
        },
      );

      return data;
    } catch (error) {
      if (isAxiosError(error)) {
        throw new InternalServerErrorException(error.response.data);
      }
      logger.error('BoletoPaymentCelcoinService.perform() -> ', error);
    }
  }
}
