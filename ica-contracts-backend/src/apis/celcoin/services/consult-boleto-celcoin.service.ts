import { Inject, InternalServerErrorException } from '@nestjs/common';
import * as AWS from 'aws-sdk';
import axios, { isAxiosError } from 'axios';
import * as https from 'https';

import { IBoletoConsultCelcoin } from '../requests/boleto-consult-celcoin.request';
import { AuthCelcoinService } from './auth-celcoin.service';

export class ConsultBoletoCelcoinService {
  constructor(
    @Inject(AuthCelcoinService)
    private authCelcoinService: AuthCelcoinService,
  ) {}

  async perform(input: IBoletoConsultCelcoin) {
    try {
      const { accessToken: token } = await this.authCelcoinService.getToken();
      const s3 = new AWS.S3({
        accessKeyId: process.env.AWS_ACCESS_KEY,
        secretAccessKey: process.env.AWS_SECRET_KEY,
        region: process.env.AWS_REGION,
      });
      const cert = await s3
        .getObject({
          Bucket: 'keys-icabank',
          Key: 'newinvest_certificado.crt',
        })
        .promise();

      const key = await s3
        .getObject({
          Bucket: 'keys-icabank',
          Key: 'newinvest_chave_decrypted.key',
        })
        .promise();

      const httpsAgent = new https.Agent({
        cert: cert.Body as any,
        key: key.Body as any,
      });

      const { data } = await axios.post(
        `${process.env.CELCOIN_URL}/v5/transactions/billpayments/authorize`,
        {
          barCode: {
            type: 0,
            barCode: input.barCode,
            digitable: input.digitable,
          },
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
          httpsAgent,
          transformResponse: [this.transformBigInt],
        },
      );

      return data;
    } catch (error) {
      if (isAxiosError(error)) {
        throw new InternalServerErrorException(error.response.data);
      }
    }
  }

  private transformBigInt(data) {
    return data.replace(/"transactionId":(\d+)/g, '"transactionId":"$1"');
  }
}
