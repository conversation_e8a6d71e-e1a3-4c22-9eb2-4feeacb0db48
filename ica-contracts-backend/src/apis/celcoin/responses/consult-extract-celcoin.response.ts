export type Movement = {
  id: string;
  clientCode: string;
  description: string;
  createDate: string;
  lastUpdateDate: string;
  status: string;
  balanceType: string;
  movementType: string;
  amount: number;
};

export interface IConsultExtractCelcoinResponse {
  status?: string;
  version?: string;
  dateFrom?: string;
  dateTo?: string;
  totalItems?: number;
  currentPage?: number;
  totalPages?: number;
  body: {
    account: string;
    documentNumber: string;
    movements: Movement[] | [];
  };
}
