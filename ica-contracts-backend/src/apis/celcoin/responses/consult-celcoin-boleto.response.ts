export interface IConsultCelcoinBoletoResponse {
  body: IBody;
  version: string;
  status: string;
}

interface IBody {
  transactionId: string;
  externalId: string;
  amount: number;
  duedate: string;
  status: string;
  debtor: IDebtor;
  receiver: IReceiver;
  boleto: IBoleto;
  pix: IPix;
  split: any[];
}

interface IDebtor {
  name: string;
  document: string;
  postalCode: string;
  publicArea: string;
  number: string;
  neighborhood: string;
  city: string;
  state: string;
}

interface IReceiver {
  name: string;
  document: string;
  postalCode: string;
  publicArea: string;
  city: string;
  state: string;
  account: string;
}

export interface IBoleto {
  transactionId: string;
  status: string;
  bankEmissor: string;
  bankNumber: string;
  bankAgency: string;
  bankAccount: string;
  barCode: string;
  bankLine: string;
  bankAssignor: string;
}

interface IPix {
  transactionId: string;
  transactionIdentification: string;
  status: string;
  key: string;
  emv: string;
}
