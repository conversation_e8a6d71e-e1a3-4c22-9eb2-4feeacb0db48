export interface IClaimerAccount {
  participant: string;
  branch: string;
  account: string;
  accountType: string;
}

export interface IClaimer {
  personType: string;
  taxId: string;
  name: string;
}

export interface IDonorAccount {
  account: string;
  branch: string;
  taxId: string;
  name: string;
}

export interface IRequestClaimPixKeyCelcoinResponse {
  version: string;
  status: string;
  body: {
    id: string;
    claimType: 'OWNERSHIP' | 'PORTABILITY';
    key: string;
    keyType: 'CPF' | 'CNPJ' | 'EMAIL' | 'PHONE';
    claimerAccount: IClaimerAccount;
    claimer: IClaimer;
    donorParticipant: string;
    createTimestamp: string;
    completionPeriodEnd: string;
    resolutionPeriodEnd: string;
    lastModified: string;
    confirmReason: string;
    cancelReason: string;
    cancelledBy: string;
    donorAccount: IDonorAccount;
  };
}
