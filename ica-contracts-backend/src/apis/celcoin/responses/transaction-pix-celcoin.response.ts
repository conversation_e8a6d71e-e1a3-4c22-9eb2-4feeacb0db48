interface IDebitParty {
  account: string;
  branch: string;
  taxId: string;
  name: string;
  accountType: string;
}

interface ICreditParty {
  bank: string;
  key: string | null;
  account: string;
  branch: string;
  taxId: string;
  name: string;
  accountType: string;
}

interface IBody {
  id: string;
  amount: number;
  clientCode: string;
  transactionIdentification: string | null;
  endToEndId: string;
  initiationType: string;
  paymentType: string;
  urgency: string;
  transactionType: string;
  debitParty: IDebitParty;
  creditParty: ICreditParty;
  remittanceInformation: string;
}

export interface ITransactionPixCelcoinResponse {
  status: string;
  version: string;
  body: IBody;
}
