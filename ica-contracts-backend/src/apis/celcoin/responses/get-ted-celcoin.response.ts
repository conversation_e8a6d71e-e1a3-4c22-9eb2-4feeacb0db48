export interface IGetTedCelcoinResponse {
  status: string;
  version: string;
  body: {
    id: string;
    amount: number;
    clientCode: string;
    debitParty: {
      account: string;
      branch: string;
      taxId: string;
      name: string;
      accountType: string;
      personType: string;
      bank: string;
    };
    creditParty: {
      bank: string;
      account: string;
      branch: string;
      taxId: string;
      name: string;
      accountType: string;
      personType: string;
    };
    description: string;
    error?: {
      errorCode: string;
      message: string;
    };
  };
}
