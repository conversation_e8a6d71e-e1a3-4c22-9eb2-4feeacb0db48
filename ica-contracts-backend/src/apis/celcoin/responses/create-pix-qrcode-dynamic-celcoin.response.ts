export interface ICreatePixQRCodeDynamicCelcoinResponse {
  version: string;
  status: number;
  body: {
    clientRequestId: string;
    pactualId: string;
    transactionId: string;
    createTimestamp: string;
    lastUpdateTimestamp: string;
    entity: string;
    status: string;
    tags: string;
    transactionIdentification: string;
    body: {
      key: string;
      revision: string;
      location: string;
      debtor: {
        name: string;
        cpf: string;
        cnpj: string;
      };
      amount: {
        original: number;
      };
      calendar: {
        expiration: number;
        dueDate: string;
      };
      dynamicBRCodeData: {
        pointOfInitiationMethod: string;
        payloadFormatIndicator: string;
        countryCode: string;
        merchantName: string;
        merchantCity: string;
        transactionIdentification: string;
        transactionAmount: string;
        emvqrcps: string;
        merchantCategoryCode: number;
        transactionCurrency: number;
        merchantAccountInformation: {
          url: string;
        };
      };
      additionalInformation: string;
    };
  };
}
