interface IAccount {
  participant: string;
  branch: string;
  account: string;
  accountType: string;
  createDate: string;
}

interface IOwner {
  type: string;
  documentNumber: string;
  name: string;
}

interface ICounter {
  type: string;
  by: string;
  d3: string;
  d30: string;
  m6: string;
}

interface IStatistics {
  lastUpdated: string;
  counters: ICounter[];
}

interface IBody {
  keyType: string;
  key: string;
  account: IAccount;
  owner: IOwner;
  endtoEndId: string;
  statistics: IStatistics;
}

export interface IGetAccountExternalPixKeyCelcoinResponse {
  status: string;
  body: IBody;
  version: string;
}
