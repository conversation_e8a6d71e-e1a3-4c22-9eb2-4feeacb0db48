import axios, { AxiosResponse, isAxiosError } from 'axios';
import { Injectable, HttpException, BadRequestException } from '@nestjs/common';
import { ResubmitRejectedContractRequest } from '../request/resubmit-rejected-contract.request';
import { ResubmitRejectedContractResponseDto } from '../request/resubmit-rejected-contract.response';
import { mapMimeTypeToType } from 'src/shared/functions/map-mimetype';

@Injectable()
export class ResubmitRejectedContractApiService {
  async perform(
    id: string,
    dto: ResubmitRejectedContractRequest,
  ): Promise<ResubmitRejectedContractResponseDto> {
    try {
      const url = `${process.env.API_CONTRACT_SERVICE_URL}/contracts/${id}/resubmit`;
    
      console.log(`${url} - Reenviando contrato com ID: ${id}`);
        
      const formData = new FormData();

      if (dto.individual) {
        this.appendFormData(formData, dto.individual, 'individual');
      }

      if (dto.company) {
        this.appendFormData(formData, dto.company, 'company');
      }

      if (dto.bankAccount) {
        formData.append('bankAccount[bank]', dto.bankAccount.bank);
        formData.append('bankAccount[agency]', dto.bankAccount.agency);
        formData.append('bankAccount[account]', dto.bankAccount.account);
        formData.append(
          'bankAccount[accountType]',
          dto.bankAccount.accountType,
        );
        formData.append('bankAccount[pix]', dto.bankAccount.pix);
      }

      if (dto.personType) {
        formData.append('personType', dto.personType);
      }

      if (dto.contractType) {
        formData.append('contractType', dto.contractType);
      }

      if (dto.investment) {
        formData.append('investment[amount]', dto.investment.amount.toString());
        formData.append(
          'investment[monthlyRate]',
          dto.investment.monthlyRate.toString(),
        );
        formData.append(
          'investment[paymentMethod]',
          dto.investment.paymentMethod,
        );
        formData.append('investment[profile]', dto.investment.profile);
        formData.append('investment[startDate]', dto.investment.startDate);
        formData.append('investment[endDate]', dto.investment.endDate);
        formData.append(
          'investment[durationInMonths]',
          dto.investment.durationInMonths.toString(),
        );
        formData.append(
          'investment[isDebenture]',
          dto.investment.isDebenture.toString(),
        );
      }

      if (dto.proofOfPayment) {
        const proofOfPaymentBlob = new Blob([dto.proofOfPayment.buffer], {
          type: dto.proofOfPayment.mimetype,
        });
        formData.append(
          'proofOfPayment',
          proofOfPaymentBlob,
          `proofOfPayment.${mapMimeTypeToType(dto.proofOfPayment.mimetype)}`,
        );
      }

      if (dto.proofOfResidence) {
        const proofOfResidenceBlob = new Blob([dto.proofOfResidence.buffer], {
          type: dto.proofOfResidence.mimetype,
        });
        formData.append(
          'proofOfResidence',
          proofOfResidenceBlob,
          `proofOfResidence.${mapMimeTypeToType(dto.proofOfResidence.mimetype)}`,
        );
      }

      if (dto.personalDocument) {
        const personalDocumentBlob = new Blob([dto.personalDocument.buffer], {
          type: dto.personalDocument.mimetype,
        });
        formData.append(
          'personalDocument',
          personalDocumentBlob,
          `personalDocument.${mapMimeTypeToType(dto.personalDocument.mimetype)}`,
        );
      }

      if (dto.contract) {
        const contractBlob = new Blob([dto.contract.buffer], {
          type: dto.contract.mimetype,
        });
        formData.append(
          'contract',
          contractBlob,
          `contract.${mapMimeTypeToType(dto.contract.mimetype)}`,
        );
      }

      if (dto.contractType === 'SCP' && dto.investment.quotaQuantity) {
        formData.append(
          'investment[quotaQuantity]',
          dto.investment.quotaQuantity,
        );
      }
        
      const { data } = await axios.put<
        FormData,
        AxiosResponse<ResubmitRejectedContractResponseDto>
      >(url, formData, { headers: { 'Content-Type': 'multipart/form-data' } });
              
      return data;
    } catch (error) {
      if (isAxiosError(error)) {
        if (error.status > 399 && error.status < 499) {
          throw new BadRequestException(error.response.data.message);
        }

        throw new HttpException(
          error.response.data.message,
          error.response.data.statusCode,
        );
      }
        
      console.log(
        `Erro ao reenviar contrato: ${error.message}`,
        error.stack,
      );
      
      throw new HttpException(
        error.response.data.message,
        error.response.data.statusCode,
      );
    }
  }

  /**
   * Função utilitária que percorre recursivamente um objeto e adiciona cada propriedade ao FormData.
   * @param formData - Instância de FormData onde os dados serão adicionados.
   * @param data - Objeto a ser adicionado.
   * @param parentKey - Chave pai, utilizada para montar a chave composta.
   */
  private appendFormData(
    formData: FormData,
    data: any,
    parentKey?: string,
  ): void {
    if (
      data &&
      typeof data === 'object' &&
      !(data instanceof Buffer) &&
      !(data instanceof Blob)
    ) {
      Object.keys(data).forEach((key) => {
        const value = data[key];
        const fullKey = parentKey ? `${parentKey}[${key}]` : key;

        if (
          value &&
          typeof value === 'object' &&
          !(value instanceof Buffer) &&
          !(value instanceof Blob)
        ) {
          this.appendFormData(formData, value, fullKey);
        } else {
          formData.append(
            fullKey,
            value !== null && value !== undefined ? value.toString() : '',
          );
        }
      });
    } else if (parentKey) {
      formData.append(
        parentKey,
        data !== null && data !== undefined ? data.toString() : '',
      );
    }
  }
}
