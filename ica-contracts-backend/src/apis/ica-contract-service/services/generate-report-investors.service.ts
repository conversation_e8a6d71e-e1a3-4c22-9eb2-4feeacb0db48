import axios from 'axios';
import { Injectable, HttpException } from '@nestjs/common';
import { GenerateReportInvestorsDto } from 'src/modules/income-report/dto/generate-report-investors.dto';

@Injectable()
export class GenerateReportInvestorsService {
  async perform({
    investorIds,
    year,
  }: GenerateReportInvestorsDto): Promise<void> {
    try {
      const url = `${process.env.API_CONTRACT_SERVICE_URL}/request-income-report`;

      await axios.post(url, { investorIds, year });
    } catch (error) {
      throw new HttpException(
        error.response.data.message,
        error.response.data.statusCode,
      );
    }
  }
}
