import axios, { AxiosResponse, isAxiosError } from 'axios';
import { Injectable, HttpException, BadRequestException } from '@nestjs/common';
import { CreateNewContractDTO } from '../request/create-new-contract.request';

@Injectable()
export class CreateNewContractService {
  async perform(paylaod: CreateNewContractDTO): Promise<{
    id: string;
    status: string;
  }> {
    try {
      if (!process.env.API_CONTRACT_SERVICE_URL) {
        console.error('❌ API_CONTRACT_SERVICE_URL não está configurada');
        throw new BadRequestException('Configuração de API externa não encontrada');
      }

      const url = `${process.env.API_CONTRACT_SERVICE_URL}/contracts/new`;

      console.log('🔍 CreateNewContractService - URL:', url);
      console.log('🔍 CreateNewContractService - Payload:', JSON.stringify(paylaod, null, 2));

      // Primeiro, vamos testar se o serviço está acessível
      try {
        console.log('🔍 Testando conectividade com o serviço...');
        const healthCheck = await axios.get(`${process.env.API_CONTRACT_SERVICE_URL}/health`, {
          timeout: 5000
        });
        console.log('✅ Serviço acessível:', healthCheck.status);
      } catch (healthError) {
        console.warn('⚠️ Health check falhou, tentando continuar:', healthError.message);
      }

      const response = await axios.post<
        CreateNewContractDTO,
        AxiosResponse<{ id: string; status: string }>
      >(url, paylaod, {
        timeout: 30000, // 30 segundos de timeout
        headers: {
          'Content-Type': 'application/json',
        }
      });

      console.log('📋 CreateNewContractService - Full Response:', {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
        data: response.data
      });

      if (!response.data) {
        console.error('❌ CreateNewContractService - Response data is undefined');
        throw new BadRequestException('API externa retornou resposta vazia');
      }

      if (!response.data.id || !response.data.status) {
        console.error('❌ CreateNewContractService - Response data missing required fields:', response.data);
        throw new BadRequestException('API externa retornou dados incompletos');
      }

      console.log('✅ CreateNewContractService - Success:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ CreateNewContractService - Error:', error);

      if (isAxiosError(error)) {
        console.error('❌ Axios Error Details:', {
          message: error.message,
          code: error.code,
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data
        });

        if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
          throw new BadRequestException(`Não foi possível conectar ao serviço de contratos: ${error.message}`);
        }

        if (error.response) {
          if (error.response.status >= 400 && error.response.status < 500) {
            throw new BadRequestException(error.response.data || error.message);
          }

          throw new HttpException(
            error.response.data?.message || error.message,
            error.response.status,
          );
        }

        throw new BadRequestException(`Erro de rede: ${error.message}`);
      }

      console.error('❌ Non-Axios Error:', error);
      throw new HttpException(
        error.message || 'Erro interno do servidor',
        500,
      );
    }
  }
}
