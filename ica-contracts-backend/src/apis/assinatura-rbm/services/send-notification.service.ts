import {
  HttpException,
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import * as FormData from 'form-data';

import { AuthIntegrationService } from './auth.service';

@Injectable()
export class SendNotificationService {
  private readonly axiosInstance: AxiosInstance;
  private readonly apiUrl: string;
  private readonly logger = new Logger(SendNotificationService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly authIntegrationService: AuthIntegrationService,
  ) {
    this.apiUrl = `${this.configService.get<string>('API_ASSINATURA_URL')}/signatario/sendNotification`;

    this.axiosInstance = axios.create({
      baseURL: this.apiUrl,
    });
  }

  async sendNotification(documentId: string, signatarieId: string) {
    try {
      const authResponse =
        await this.authIntegrationService.authenticateIntegration();
      const { token } = authResponse.jwt;

      const formData = new FormData();
      formData.append('documento_id', documentId);
      formData.append('signatario_id', signatarieId);
      formData.append('method', 'email');

      const response = await this.axiosInstance.post('', formData, {
        headers: {
          ...formData.getHeaders(),
          Authorization: `Bearer ${token}`,
        },
      });

      this.logger.log(
        `Notificação por e-mail enviada com sucesso para signatário ${signatarieId}.`,
      );
      return response.data;
    } catch (error) {
      if (error.response) {
        throw new HttpException(
          `${error.response.data.message || error.response.data}`,
          error.response.status,
        );
      } else if (error.request) {
        throw new InternalServerErrorException(
          'Nenhuma resposta recebida da API.',
        );
      } else {
        throw new InternalServerErrorException(
          `Erro na requisição: ${error.message}`,
        );
      }
    }
  }
}
