import {
  Injectable,
  InternalServerErrorException,
  Logger,
  UnprocessableEntityException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import * as FormData from 'form-data';
import { createReadStream } from 'fs';

import { ISendDocumentRequest } from '../requests/send-document.request';
import { ISendDocumentResponse } from '../responses/send-document.response';
import { AuthIntegrationService } from './auth.service';

@Injectable()
export class SendDocumentService {
  private readonly axiosInstance: AxiosInstance;
  private readonly apiUrl: string;
  private readonly logger = new Logger(SendDocumentService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly authIntegrationService: AuthIntegrationService,
  ) {
    this.apiUrl = `${this.configService.get<string>('API_ASSINATURA_URL')}/documentos`;

    this.axiosInstance = axios.create({
      baseURL: this.apiUrl,
    });
  }

  async sendDocument(
    data: ISendDocumentRequest,
  ): Promise<ISendDocumentResponse> {
    const currentDate = new Date().getDay();

    if (process.env.NODE_ENV === 'development' && currentDate > 5) {
      throw new UnprocessableEntityException(
        'Indisponível aos finais de semana',
      );
    }

    const authResponse =
      await this.authIntegrationService.authenticateIntegration();
    const { token } = authResponse.jwt;

    const formData = new FormData();
    formData.append('arquivo', createReadStream(data.filePath));
    if (data.numero) formData.append('numero', data.numero);
    if (data.extra_info) formData.append('extra_info', data.extra_info);
    if (data.tag) formData.append('tag', data.tag);
    if (data.quantidade_registros !== undefined)
      formData.append(
        'quantidade_registros',
        data.quantidade_registros.toString(),
      );
    if (data.finalizacao_automatica)
      formData.append('finalizacao_automatica', data.finalizacao_automatica);
    if (data.data_limite) formData.append('data_limite', data.data_limite);
    if (data.ordem_assinatura_utilizar)
      formData.append(
        'ordem_assinatura_utilizar',
        data.ordem_assinatura_utilizar,
      );

    try {
      const response = await this.axiosInstance.post<ISendDocumentResponse>(
        '',
        formData,
        {
          headers: {
            ...formData.getHeaders(),
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (response.data.erro) {
        this.logger.error(
          `Erro ao registrar documento: ${response.data.message}`,
        );
        throw new InternalServerErrorException(response.data.message);
      }

      return response.data;
    } catch (error) {
      this.logger.error('Erro ao enviar documento', error);

      if (error.response) {
        throw new InternalServerErrorException(
          `Erro da API: ${error.response.status} - ${error.response.data.message || error.response.data}`,
        );
      } else if (error.request) {
        throw new InternalServerErrorException(
          'Nenhuma resposta recebida da API.',
        );
      } else {
        throw new InternalServerErrorException(
          `Erro na requisição: ${error.message}`,
        );
      }
    }
  }
}
