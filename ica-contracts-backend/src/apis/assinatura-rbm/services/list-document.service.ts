import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';

import { AuthIntegrationService } from './auth.service';

@Injectable()
export class DocumentInfoService {
  private readonly axiosInstance: AxiosInstance;
  private readonly apiUrl: string;
  private readonly logger = new Logger(DocumentInfoService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly authIntegrationService: AuthIntegrationService,
  ) {
    this.apiUrl = `${this.configService.get<string>('API_ASSINATURA_URL')}/v2/documentos/infos`;

    this.axiosInstance = axios.create({
      baseURL: this.apiUrl,
    });
  }

  async getDocumentInfo(id: number) {
    const authResponse =
      await this.authIntegrationService.authenticateIntegration();
    const { token } = authResponse.jwt;

    try {
      const response = await this.axiosInstance.get<{
        erro: boolean;
        payload: any;
      }>(`/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.data.erro) {
        this.logger.error(
          `Error fetching document info: ${response.data.payload}`,
        );
        throw new InternalServerErrorException(response.data.payload);
      }

      return response.data.payload;
    } catch (error) {
      if (error.response) {
        throw new InternalServerErrorException(
          `API error: ${error.response.status} - ${error.response.data.message || error.response.data}`,
        );
      } else if (error.request) {
        throw new InternalServerErrorException(
          'No response received from API.',
        );
      } else {
        throw new InternalServerErrorException(
          `Request error: ${error.message}`,
        );
      }
    }
  }
}
