import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import * as FormData from 'form-data';

import { IFindSignatarieResponse } from '../responses/find-signatarie.response';
import { AuthIntegrationService } from './auth.service';

@Injectable()
export class FindSignatarieService {
  private readonly axiosInstance: AxiosInstance;
  private readonly apiUrl: string;
  private readonly logger = new Logger(FindSignatarieService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly authIntegrationService: AuthIntegrationService,
  ) {
    this.apiUrl = `${this.configService.get<string>('API_ASSINATURA_URL')}/signatario/find`;

    this.axiosInstance = axios.create({
      baseURL: this.apiUrl,
    });
  }

  async findSignatarie(document: string): Promise<IFindSignatarieResponse> {
    try {
      const authResponse =
        await this.authIntegrationService.authenticateIntegration();
      const { token } = authResponse.jwt;

      const formData = new FormData();
      formData.append('cpfCnpj', document);
      const response = await this.axiosInstance.post<IFindSignatarieResponse>(
        '',
        formData,
        {
          headers: {
            ...formData.getHeaders(),
            Authorization: `Bearer ${token}`,
          },
        },
      );

      return response.data;
    } catch (error) {
      console.log(error);

      this.logger.error('Erro ao autenticar integração', error);

      if (error.response) {
        throw new InternalServerErrorException(
          `Erro da API: ${error.response.status} - ${error.response.data.message || error.response.data}`,
        );
      } else if (error.request) {
        throw new InternalServerErrorException(
          'Nenhuma resposta recebida da API.',
        );
      } else {
        throw new InternalServerErrorException(
          `Erro na requisição: ${error.message}`,
        );
      }
    }
  }
}
