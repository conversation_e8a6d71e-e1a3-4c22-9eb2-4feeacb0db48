interface ISignatory {
  nome: string;
  cpfCnpj: string;
  email?: string;
  celular?: string;
  dataNasc?: string;
  tipoAssinatura: string;
  tipoAssinaturaComplemento?: string;
  grupoAssinaturaId?: number;
  ordemAssinatura?: number;
  tipoAutenticacao: string;
  selfieNecessaria?: string;
  documentoFrenteNecessario?: string;
  documentoVersoNecessario?: string;
  selfieComDocumentoNecessario?: string;
  certificadoDigitalNecessario?: string;
  serproRealizarValidacaoFacial?: string;
  faceMatch?: string;
}

export interface IAddSignatoriesRequest {
  mensagem: string;
  signatarios: ISignatory[];
}
