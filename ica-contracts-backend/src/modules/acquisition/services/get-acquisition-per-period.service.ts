import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as moment from 'moment';
import { Repository } from 'typeorm';

import { ContractEntity } from '../../../shared/database/typeorm/entities/contract.entity';
import { ContractStatusEnum } from 'src/shared/enums/contract-status.enum';

@Injectable()
export class GetAcquisitionPerPeriodService {
  constructor(
    @InjectRepository(ContractEntity)
    private readonly contractRepository: Repository<ContractEntity>,
  ) {}

  async perform(period: 'daily' | 'weekly' | 'monthly' | 'early') {
    const { startDate, endDate } = this.getDateRange(period);
    const contracts = await this.fetchContractsByDateRange(startDate, endDate);
    return this.calculateCaptationData(contracts);
  }

  private getDateRange(period: 'daily' | 'weekly' | 'monthly' | 'early') {
    const now = moment().startOf('day');

    if (period === 'daily') {
      return {
        startDate: now.toDate(),
        endDate: now.endOf('day').toDate(),
      };
    }

    if (period === 'weekly') {
      return {
        startDate: now.startOf('week').toDate(),
        endDate: now.endOf('week').toDate(),
      };
    }

    if (period === 'monthly') {
      return {
        startDate: now.startOf('month').toDate(),
        endDate: now.endOf('month').toDate(),
      };
    }

    if (period === 'early') {
      return {
        startDate: now.startOf('year').toDate(),
        endDate: now.endOf('year').toDate(),
      };
    }
  }

  private async fetchContractsByDateRange(startDate: Date, endDate: Date) {
    return this.contractRepository
      .createQueryBuilder('contract')
      .leftJoinAndSelect('contract.signataries', 'signataries')
      .where(
        'CAST(contract.startContract AS DATE) BETWEEN CAST(:startDate AS DATE) AND CAST(:endDate AS DATE)',
        { startDate, endDate },
      )
      .andWhere('contract.status = :status', {
        status: ContractStatusEnum.ACTIVE,
      })
      .getMany();
  }

  private calculateCaptationData(contracts: ContractEntity[]) {
    const totalContracts = contracts.length;
    const totalValue =
      contracts.reduce((sum, contract) => {
        const contractValue = contract.signataries.reduce((acc, sig) => {
          const investmentValue =
            Number(sig.investmentValue as unknown as string) * 100;
          return acc + investmentValue;
        }, 0);

        return sum + contractValue;
      }, 0) / 100;

    const details = contracts.map((contract) => ({
      id: contract.id,
      startDate: contract.startContract,
      endDate: contract.endContract,
      totalInvestment: contract.signataries.reduce((acc, sig) => {
        const investmentValue = parseFloat(
          sig.investmentValue as unknown as string,
        );
        return acc + investmentValue;
      }, 0),
      createdAt: contract.createdAt,
    }));

    return {
      totalContracts,
      totalValue,
      details,
    };
  }
}
