import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as moment from 'moment';
import { Repository } from 'typeorm';

import { ContractEntity } from '../../../shared/database/typeorm/entities/contract.entity';
import { ContractStatusEnum } from 'src/shared/enums/contract-status.enum';

@Injectable()
export class GetAcquisitionChartDataService {
  constructor(
    @InjectRepository(ContractEntity)
    private readonly contractRepository: Repository<ContractEntity>,
  ) {}

  async perform(period: 'weekly' | 'monthly') {
    const { startDate, endDate, groupBy } = this.getChartDateRange(period);
    const data = await this.fetchChartData(startDate, endDate, groupBy);

    if (period === 'monthly') {
      return this.generateMonthlyData(data);
    }
    if (period === 'weekly') {
      return this.generateWeeklyData(data);
    }

    return {
      period,
      data,
    };
  }

  private getChartDateRange(period: 'weekly' | 'monthly'): {
    startDate: Date;
    endDate: Date;
    groupBy: 'MONTH' | 'DAY';
  } {
    const now = moment().startOf('day');

    if (period === 'weekly') {
      return {
        startDate: now.startOf('week').toDate(),
        endDate: now.endOf('week').toDate(),
        groupBy: 'DAY', // Group by day for weekly chart
      };
    }

    if (period === 'monthly') {
      return {
        startDate: now.startOf('year').toDate(),
        endDate: now.endOf('year').toDate(),
        groupBy: 'MONTH', // Group by month for monthly chart
      };
    }

    throw new Error('Invalid period');
  }

  private async fetchChartData(
    startDate: Date,
    endDate: Date,
    groupBy: 'DAY' | 'MONTH',
  ) {
    const query = this.contractRepository
      .createQueryBuilder('contract')
      .leftJoinAndSelect('contract.signataries', 'signataries')
      .leftJoin('contract.addendum', 'addendum')
      .select(
        `DATE_TRUNC('${groupBy.toLowerCase()}', contract.startContract) AS period`,
      )
      .addSelect('COUNT(contract.id)', 'totalContracts')
      .addSelect(
        'SUM(signataries.investment_value::numeric) + COALESCE(SUM(addendum.value), 0)',
        'totalValue',
      )
      .where(
        'CAST(contract.startContract AS DATE) BETWEEN CAST(:startDate AS DATE) AND CAST(:endDate AS DATE)',
        { startDate, endDate },
      )
      .andWhere('contract.status = :status', {
        status: ContractStatusEnum.ACTIVE,
      })
      .andWhere('contract.endContract >= :currentDate', {
        currentDate: new Date(),
      });

    return query
      .groupBy(`DATE_TRUNC('${groupBy.toLowerCase()}', contract.startContract)`)
      .orderBy(
        `DATE_TRUNC('${groupBy.toLowerCase()}', contract.startContract)`,
        'ASC',
      )
      .getRawMany();
  }

  private generateMonthlyData(
    data: { period: Date; totalContracts: number; totalValue: number }[],
  ) {
    const months = moment.months();
    const currentYear = moment().year();

    const result = months.map((month, index) => ({
      month,
      totalContracts: 0,
      totalValue: 0,
      date: moment([currentYear, index]).toISOString(),
    }));

    data.forEach((item) => {
      const monthIndex = moment(
        item.period.toISOString().split('T').at(0),
      ).month();
      result[monthIndex].totalContracts = Number(item.totalContracts);
      result[monthIndex].totalValue = item.totalValue;
    });

    return result;
  }

  private generateWeeklyData(
    data: { period: string; totalContracts: number; totalValue: number }[],
  ) {
    const startOfWeek = moment().startOf('week');
    const endOfWeek = moment().endOf('week');

    const result = [];
    const currentDay = startOfWeek.clone();

    while (
      currentDay.isBefore(endOfWeek) ||
      currentDay.isSame(endOfWeek, 'day')
    ) {
      result.push({
        day: currentDay.format('dddd'),
        date: currentDay.toISOString(),
        totalContracts: 0,
        totalValue: 0,
      });

      currentDay.add(1, 'day');
    }

    data.forEach((item) => {
      const dayIndex = result.findIndex((r) =>
        moment(r.date).isSame(moment(item.period), 'day'),
      );
      if (dayIndex !== -1) {
        result[dayIndex].totalContracts = Number(item.totalContracts);
        result[dayIndex].totalValue = item.totalValue;
      }
    });

    return { period: 'weekly', data: result };
  }
}
