import { Module } from '@nestjs/common';

import { SharedModule } from '../../shared/shared.module';
import { AcquisitionController } from './controllers/acquisition.controller';
import { GetAcquisitionChartDataService } from './services/get-acquisition-chart-data.service';
import { GetAcquisitionPerPeriodService } from './services/get-acquisition-per-period.service';

@Module({
  controllers: [AcquisitionController],
  providers: [GetAcquisitionPerPeriodService, GetAcquisitionChartDataService],
  imports: [SharedModule],
})
export class AcquisitionModule {}
