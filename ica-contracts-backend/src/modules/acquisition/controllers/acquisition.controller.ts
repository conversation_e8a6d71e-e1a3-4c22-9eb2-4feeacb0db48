import {
  BadRequestException,
  Controller,
  Get,
  Query,
  UseGuards,
} from '@nestjs/common';

import { JwtAuthGuard } from '../../../shared/guards/jwt-auth.guard';
import { GetAcquisitionChartDataService } from '../services/get-acquisition-chart-data.service';
import { GetAcquisitionPerPeriodService } from '../services/get-acquisition-per-period.service';

@UseGuards(JwtAuthGuard)
@Controller('acquisition')
export class AcquisitionController {
  constructor(
    private readonly getAcquisitionPerPeriodService: GetAcquisitionPerPeriodService,
    private readonly getAcquisitionChartDataService: GetAcquisitionChartDataService,
  ) {}

  @Get()
  async getCaptation(@Query('period') period: 'daily' | 'weekly' | 'monthly') {
    if (!period || !['daily', 'weekly', 'monthly'].includes(period)) {
      throw new BadRequestException(
        'Invalid period. Must be daily, weekly, or monthly.',
      );
    }

    return this.getAcquisitionPerPeriodService.perform(period);
  }

  @Get('/chart')
  async getChart(@Query('period') period: 'weekly' | 'monthly') {
    if (!period || !['weekly', 'monthly'].includes(period)) {
      throw new BadRequestException(
        'Invalid period. Must be weekly or monthly.',
      );
    }

    return this.getAcquisitionChartDataService.perform(period);
  }
}
