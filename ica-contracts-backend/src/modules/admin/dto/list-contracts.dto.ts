import { IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';
import { ContractStatusEnum } from 'src/shared/enums/contract-status.enum';
import { ContractTypeEnum } from 'src/shared/enums/contract-type.enum';

export class ListContractsDto {
  @IsOptional()
  page: number;

  @IsOptional()
  limit: number;

  @IsOptional()
  @IsEnum(ContractStatusEnum, {
    message: 'Status inválido',
  })
  status: ContractStatusEnum;

  @IsOptional()
  dateFrom: string;

  @IsOptional()
  dateTo: string;

  @IsOptional()
  @IsEnum(ContractTypeEnum)
  contractType: ContractTypeEnum;

  @IsOptional()
  @IsString()
  signatarie: string;
}
