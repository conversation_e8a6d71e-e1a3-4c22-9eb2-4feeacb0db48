import {
  Body,
  Controller,
  Get,
  HttpException,
  Param,
  Patch,
  Put,
  Query,
  Request,
  UseGuards,
} from '@nestjs/common';
import { Roles } from 'src/shared/decorators/roles.decorator';
import { RolesEnum } from 'src/shared/enums/roles.enum';
import { JwtAuthGuard } from 'src/shared/guards/jwt-auth.guard';
import { RoleGuard } from 'src/shared/guards/role.guard';

import { EditBrokerDto } from '../../broker/dto/edit-broker.dto';
import { EditInvestorService } from '../../investor/services/edit-investor.service';
import { AdminDashboardService } from '../services/dashboard.service';
import { EditAdminService } from '../services/edit-admin.service';
import { GetAdminService } from '../services/get-admin.service';
import { PeriodFilter } from 'src/modules/contract/helpers/get-contracts-growth-chart.absctraction';
import { GetAdminContractsGrowthChartService } from '../services/get-admin-contracts-growth-chart.service';
import { ContractTypeEnum } from 'src/shared/enums/contract-type.enum';
import type { ChangePasswordDto } from '../dto/change-password.dto';
import { ChangePasswordAdminService } from '../services/change-password.service';
import { ListContractsDto } from '../dto/list-contracts.dto';
import { ListContractsAdminService } from '../services/list-contracts.service';
import { IRequestUser } from 'src/shared/interfaces/request-user.interface';

@Controller('admin')
export class AdminController {
  constructor(
    private readonly getAdminService: GetAdminService,
    private readonly dashboardService: AdminDashboardService,
    private readonly editInvestorService: EditInvestorService,
    private readonly editAdminService: EditAdminService,
    private readonly getAdminContractsGrowthChartService: GetAdminContractsGrowthChartService,
    private readonly changePasswordService: ChangePasswordAdminService,
    private readonly listContractsAdminService: ListContractsAdminService,
  ) {}

  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.ADMIN)
  @Get('brokers')
  async search(
    @Request() request:IRequestUser 
  ) {
    const results = await this.getAdminService.perform({
      ownerId: request.user.id,
    });
    return results;
  }

  @UseGuards(JwtAuthGuard, RoleGuard)
  @Get('dashboard')
  async dashboard(
    @Request() request:IRequestUser) {
    const result = await this.dashboardService.perform({
      ownerId: request.user.id,
    });
    return result;
  }

  @UseGuards(JwtAuthGuard)
  @Put('investor')
  async editInvestor(
    @Request() request:IRequestUser,
    @Body()
    body: EditBrokerDto,
  ) {
    const result = await this.editInvestorService.perform(body, request.user.id);
    return result;
  }

  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.SUPERADMIN, RolesEnum.ADMIN)
  @Patch('')
  async editBroker(
    @Request() request:IRequestUser,
    @Body()
    body: EditBrokerDto,
  ) {
    const userId = request.user.id;

    await this.editAdminService.perform(body, userId);
  }

  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.SUPERADMIN)
  @Put('change-password')
  async changePassword(
    @Body()
    body: ChangePasswordDto,
  ) {
    console.log('chamou controller');
    try {
      const result = await this.changePasswordService.perform(body);
      return result;
    } catch (error) {
      throw new HttpException(
        { error: error.response || error.message },
        error.status,
      );
    }
  }

  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.SUPERADMIN, RolesEnum.ADMIN)
  @Get('/:ownerRoleId/contracts-growth')
  async getContractsGrowthChart(
    @Param('ownerRoleId') ownerRoleId: string,
    @Query('period') period?: PeriodFilter,
    @Query('contractType') contractType?: ContractTypeEnum,
  ) {
    return this.getAdminContractsGrowthChartService.perform(
      ownerRoleId,
      period,
      contractType,
    );
  }

  @Get('/list-contracts')
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.ADMIN)
  async listContractsAdmin(@Request() request, @Query() data: ListContractsDto) {
    return this.listContractsAdminService.perform(data, request.user.id);
  }
}
