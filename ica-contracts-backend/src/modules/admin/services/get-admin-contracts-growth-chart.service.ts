import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import {
  DateGroupType,
  GetContractsGrowthChartAbstraction,
  PeriodFilter,
} from 'src/modules/contract/helpers/get-contracts-growth-chart.absctraction';
import { AddendumStatus } from 'src/shared/database/typeorm/entities/addendum.entity';
import { WalletsViewsEntity } from 'src/shared/database/typeorm/entities/wallets-views.entity';
import { ContractStatusEnum } from 'src/shared/enums/contract-status.enum';
import { ContractTypeEnum } from 'src/shared/enums/contract-type.enum';
import { ContractEntity } from '../../../shared/database/typeorm/entities/contract.entity';

type FetchChartDataParams = {
  startDate: Date;
  endDate: Date;
  groupBy: DateGroupType;
  contractType?: ContractTypeEnum;
  brokerIds: string[];
};

@Injectable()
export class GetAdminContractsGrowthChartService extends GetContractsGrowthChartAbstraction {
  constructor(
    @InjectRepository(ContractEntity)
    private readonly contractRepository: Repository<ContractEntity>,
    @InjectRepository(WalletsViewsEntity)
    private walletsViewsRepository: Repository<WalletsViewsEntity>,
  ) {
    super();
  }

  async perform(
    ownerRoleId: string,
    periodFilter: PeriodFilter = 'year',
    contractType?: ContractTypeEnum,
  ) {
    const { startDate, endDate, groupBy } = this.getDateRange(periodFilter);
    const brokerIds = await this.getBrokersIdsByAdmin(ownerRoleId);
    const data = await this.fetchChartData({
      startDate,
      endDate,
      groupBy,
      contractType,
      brokerIds,
    });
    return this.generateChartData(data, periodFilter);
  }

  private async fetchChartData({
    startDate,
    endDate,
    groupBy,
    contractType,
    brokerIds,
  }: FetchChartDataParams) {
    const query = this.contractRepository
      .createQueryBuilder('contract')
      .leftJoin('contract.signataries', 'signataries')
      .leftJoinAndSelect(
        'contract.addendum',
        'addendum',
        'addendum.status = :addStatus',
        { addStatus: AddendumStatus.FULLY_SIGNED },
      )
      .select(
        `DATE_TRUNC('${groupBy.toLowerCase()}', contract.startContract) AS period`,
      )
      .addSelect('COUNT(contract.id)', 'totalContracts')
      .addSelect(
        'COALESCE(SUM(signataries.investment_value), 0) + COALESCE(SUM(addendum.value), 0)',
        'totalValue',
      )
      .where(
        'CAST(contract.startContract AS DATE) BETWEEN CAST(:startDate AS DATE) AND CAST(:endDate AS DATE)',
        { startDate, endDate },
      )
      .andWhere('contract.status = :status', {
        status: ContractStatusEnum.ACTIVE,
      })
      .andWhere('contract.endContract >= :currentDate', {
        currentDate: new Date(),
      });

    if (brokerIds.length) {
      query.andWhere('contract.brokerId IN (:...brokerIds)', { brokerIds });
    }

    if (contractType) {
      query.andWhere('contract.type = :type', {
        type: contractType,
      });
    }

    return query
      .groupBy(`DATE_TRUNC('${groupBy.toLowerCase()}', contract.startContract)`)
      .orderBy(
        `DATE_TRUNC('${groupBy.toLowerCase()}', contract.startContract)`,
        'ASC',
      )
      .getRawMany();
  }

  private async getBrokersIdsByAdmin(adminORRId: string) {
    const wallets = await this.walletsViewsRepository.find({
      where: {
        upperId: adminORRId,
      },
    });

    return wallets.map((wallet) => wallet.bottomId);
  }
}
