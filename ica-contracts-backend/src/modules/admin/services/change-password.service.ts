import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as brcypt from 'bcrypt';
import { BusinessEntity } from 'src/shared/database/typeorm/entities/business.entity';
import { OwnerEntity } from 'src/shared/database/typeorm/entities/owner.entity';
import { Repository } from 'typeorm';
import { EmailNgService } from 'src/shared/email-ng/email-ng.service';

import { ChangePasswordDto } from '../dto/change-password.dto';
import {
  EmailEntity,
  EmailEnum,
} from 'src/shared/database/typeorm/entities/email.entity';

@Injectable()
export class ChangePasswordAdminService {
  constructor(
    @InjectRepository(OwnerEntity)
    private ownerRepo: Repository<OwnerEntity>,
    @InjectRepository(BusinessEntity)
    private businessRepo: Repository<BusinessEntity>,
    private emailNgService: EmailNgService,
    @InjectRepository(EmailEntity)
    private emailRepository: Repository<EmailEntity>,
  ) {}

  async perform(data: ChangePasswordDto) {
    console.log('chamou service');
    const isCpf = data.document.length === 11;
    const repository = isCpf ? this.ownerRepo : this.businessRepo;
    const field = isCpf ? 'cpf' : 'cnpj';
    const plainPassword = data?.randomPassword
      ? generateRandomPassword()
      : data.newPassword;
    const entity = await repository.findOne({
      where: { [field]: data.document },
    });

    if (!entity) {
      throw new NotFoundException(`${isCpf ? 'CPF' : 'CNPJ'} não encontrado.`);
    }

    const hash = await brcypt.hash(plainPassword, 10);
    await repository.update(entity.id, {
      password: hash,
      temporaryPassword: false,
    });
    console.log('senha trocada');
    const splitName = isCpf
      ? (entity as OwnerEntity).name.split(' ')
      : (entity as BusinessEntity).fantasyName.split(' ');
    let externalId: string;
    try {
      const result = await this.emailNgService.welcomeInvestor({
        investorName: splitName[0],
        username: data.document,
        password: plainPassword,
        email: entity.email,
      });

      console.log('Email API Result:', result.status, result.data);
      externalId = result.data.id;
      console.log('Email sent successfully, External ID:', externalId);
    } catch (error) {
      console.error(
        'Failed to send email via API:',
        error.response?.data || error.message || error,
      );
      // Decide how to handle the error. Maybe re-throw, maybe return an error response?
      // For now, let's just log it and maybe skip saving the email record or set a FAILED status.
      throw new Error(
        `Failed to send welcome email: ${error.message || error}`,
      );
    }

    await this.emailRepository.save({
      externalId,
      email: entity.email,
      fromEmail: '<EMAIL>',
      subject: 'Acesse agora o app ICA Cliente',
      status: EmailEnum.PROCESSING,
      body: '',
    });

    return {
      message: 'Senha alterada com sucesso.',
      email: entity.email,
      plainPassword: data?.randomPassword ? plainPassword : undefined,
    };
  }
}

function generateRandomPassword(): string {
  const uppercaseChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const lowercaseChars = 'abcdefghijklmnopqrstuvwxyz';
  const numberChars = '0123456789';
  const specialChars = '@#%';

  // Garantir pelo menos um caractere de cada tipo
  const password = [
    uppercaseChars[Math.floor(Math.random() * uppercaseChars.length)],
    lowercaseChars[Math.floor(Math.random() * lowercaseChars.length)],
    numberChars[Math.floor(Math.random() * numberChars.length)],
    specialChars[Math.floor(Math.random() * specialChars.length)],
  ];

  // Completar até 8 caracteres com uma mistura de todos os tipos
  const allChars = uppercaseChars + lowercaseChars + numberChars + specialChars;
  while (password.length < 8) {
    password.push(allChars[Math.floor(Math.random() * allChars.length)]);
  }

  // Embaralhar a senha para não ter um padrão previsível
  for (let i = password.length - 1; i > 0; i--) {
    const j = crypto.getRandomValues(new Uint32Array(1))[0] % (i + 1);

    [password[i], password[j]] = [password[j], password[i]];
  }

  return password.join('');
}
