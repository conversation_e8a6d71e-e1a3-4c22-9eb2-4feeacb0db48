import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AdminEntity } from 'src/shared/database/typeorm/entities/admin.entity';
import { Repository } from 'typeorm';

import { GetAdminDto } from '../dto/get-admin-request.dto';

@Injectable()
export class GetAdminService {
  constructor(
    @InjectRepository(AdminEntity)
    private adminRepository: Repository<AdminEntity>,
  ) {}

  async perform(input: GetAdminDto) {
    const admin = await this.adminRepository.findOne({
      where: { ownerId: input.ownerId },
      relations: { broker: { owner: true } },
    });

    if (!admin) {
      throw new NotFoundException(`Admin with ID ${input.ownerId} not found.`);
    }

    const response = {
      admin: {
        adminId: admin.id,
        ownerId: admin.ownerId,
        broker: [],
      },
    };

    for (const broker of admin.broker) {
      const brokerObj = {
        brokerId: broker.id,
        ownerId: broker.ownerId,
        name: broker.owner?.name || null,
        cpf: broker.owner?.cpf || null,
        phone: broker.owner?.phone || null,
        email: broker.owner?.email || null,
        createdAt: broker.owner?.createdAt || null,
      };

      response.admin.broker.push(brokerObj);
    }

    return response;
  }
}
