import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';
import { Between, Equal, In, Repository } from 'typeorm';

import { AddendumStatus } from '../../../shared/database/typeorm/entities/addendum.entity';
import { ListContractsDto } from '../dto/list-contracts.dto';
import { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';
import { WalletsViewsEntity } from 'src/shared/database/typeorm/entities/wallets-views.entity';

@Injectable()
export class ListContractsAdminService {
  constructor(
    @InjectRepository(ContractEntity)
    private readonly contractRepository: Repository<ContractEntity>,
    @InjectRepository(OwnerRoleRelationEntity)
    private readonly ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>,
    @InjectRepository(WalletsViewsEntity)
    private readonly walletsViewsRepository: Repository<WalletsViewsEntity>,
  ) {}

  async perform(data: ListContractsDto, userId: string) {
    const admin = await this.ownerRoleRelationRepository.findOne({
      where: [
        { id: Equal(userId), role: { name: 'admin' } },
        {
          ownerId: Equal(userId),
          role: { name: 'admin' },
        },
        {
          businessId: Equal(userId),
          role: { name: 'admin' },
        },
      ],
    });

    const walletViews = await this.walletsViewsRepository.find({
      where: { upperId: admin.id },
      relations: { bottom: true, upper: true },
      select: ['bottomId'],
    });

    const bottomIds = walletViews.map((view) => view.bottomId);
    const allowedOwnerRelationIds = [userId, ...bottomIds];

    const page = data?.page ?? 1;
    const limit = data?.limit ?? 10;

    const skip = (page - 1) * limit;
    const take = limit;

    const baseConditions: any = {};

    if (data?.status) {
      baseConditions.status = data.status;
    }

    if (data?.signatarie) {
      baseConditions.signataries = { document: data.signatarie };
    }

    if (data?.dateFrom && data?.dateTo) {
      baseConditions.startContract = Between(
        new Date(data.dateFrom),
        new Date(data.dateTo),
      );
    } else if (data?.dateFrom) {
      baseConditions.startContract = Between(
        new Date(data.dateFrom),
        new Date(),
      );
    } else if (data?.dateTo) {
      baseConditions.startContract = Between(
        new Date('1970-01-01'),
        new Date(data.dateTo),
      );
    }

    if (data?.contractType) {
      baseConditions.signataries = {
        ...baseConditions.signataries,
        investmentModality: data.contractType,
      };
    }

    const [dbContracts, totalDbContracts] =
      await this.contractRepository.findAndCount({
        where: [
          {
            ...baseConditions,
            ownerRoleRelation: { id: In(allowedOwnerRelationIds) },
          },
        ],
        relations: {
          ownerRoleRelation: { business: true, owner: true },
          signataries: true,
          investor: true,
          addendum: {
            addendumFiles: {
              file: true,
            },
          },
        },
        skip,
        take,
      });

    const documents = dbContracts.map((dbContract) => ({
      investorId:
        dbContract.investor?.ownerId || dbContract.investor?.businessId || '',
      valorInvestimento: dbContract.signataries[0]?.investmentValue,
      prazoInvestimento: dbContract.signataries[0]?.investmentTerm,
      rendimentoInvestimento: dbContract.signataries[0]?.investmentYield,
      nomeInvestidor: dbContract.signataries[0]?.name,
      documentoInvestidor: dbContract.signataries[0]?.document,
      tags: dbContract.type,
      compradoCom: dbContract.signataries[0]?.purchaseWith,
      cotas: dbContract.signataries[0]?.amountQuotes,
      periodoCarencia: new Date(dbContract.signataries[0]?.gracePeriod)
        .getUTCDate()
        .toString(),
      consultorResponsavel:
        dbContract.ownerRoleRelation?.owner?.name ||
        dbContract.ownerRoleRelation?.business?.fantasyName,
      idContrato: dbContract.id,
      statusContrato: dbContract.status,
      inicioContrato: dbContract.startContract,
      fimContrato: dbContract.endContract,
      contratoPdf: dbContract.contractPdf,
      comprovamentePagamento: dbContract.proofPayment,
      advisorParticipationPercentage: dbContract.advisorParticipationPercentage,
      brokerParticipationPercentage: dbContract.brokerParticipationPercentage,
      addendum: dbContract.addendum.map((addendum) => ({
        ...addendum,
        addendumFiles: addendum.addendumFiles.map((addendumFile) => ({
          type: addendumFile.type,
          url: addendumFile.file.url,
        })),
      })),
    }));

    return {
      total: totalDbContracts,
      paginaAtual: page,
      totalPaginas: Math.ceil(totalDbContracts / limit),
      totalPorPagina: documents.length < limit ? documents.length : limit,
      documentos: documents,
    };
  }
}
