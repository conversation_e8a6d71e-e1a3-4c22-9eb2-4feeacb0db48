import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';
import { WalletsViewsEntity } from 'src/shared/database/typeorm/entities/wallets-views.entity';
import { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';
import { ContractStatusEnum } from 'src/shared/enums/contract-status.enum';
import { RolesEnum } from 'src/shared/enums/roles.enum';
import { Repository, Equal, In } from 'typeorm';

import { AdminDashboardRequestDto } from '../dto/admin-dashboard-request-dto';

@Injectable()
export class AdminDashboardService {
  constructor(
    @InjectRepository(OwnerRoleRelationEntity)
    private ownerRelationRepository: Repository<OwnerRoleRelationEntity>,
    @InjectRepository(WalletsViewsEntity)
    private walletsViewsRepository: Repository<WalletsViewsEntity>,
    @InjectRepository(ContractEntity)
    private readonly contractRepository: Repository<ContractEntity>,
  ) {}

  async perform({ ownerId }: AdminDashboardRequestDto) {
    const admin = await this.ownerRelationRepository.findOne({
      relations: {
        role: true,
      },
      where: [
        { id: Equal(ownerId), role: { name: 'admin' } },
        { ownerId: Equal(ownerId), role: { name: 'admin' } },
        { businessId: Equal(ownerId), role: { name: 'admin' } },
      ],
    });

    if (!admin) {
      throw new ConflictException('Admin not found!');
    }

    // Buscar todos os ids de relação do admin (caso haja estrutura de subcontas)
    const walletViews = await this.walletsViewsRepository.find({
      where: { upperId: admin.id },
      relations: { bottom: true, upper: true },
      select: ['bottomId'],
    });

    const bottomIds = walletViews.map((view) => view.bottomId);
    const allowedOwnerRelationIds = [ownerId, ...bottomIds];

    // Buscar contratos ativos do admin e seus subordinados
    const adminContracts = await this.contractRepository.find({
      where: [
        {
          status: ContractStatusEnum.ACTIVE,
          ownerRoleRelation: { id: In(allowedOwnerRelationIds) },
        },
      ],
      relations: {
        ownerRoleRelation: { business: true, owner: true },
        signataries: true,
        investor: true,
        addendum: true,
      },
    });

    // Filtrar apenas contratos com datas válidas
    const activeContracts = adminContracts.filter((c) =>
      this.isContractDateValid(c.startContract, c.endContract),
    );

    let p2pContractAmount = 0;
    let scpContractAmount = 0;
    let uniqueInvestors = 0;
    let p2pContractNumber = 0;
    let scpContractNumber = 0;
    let totalContracts = activeContracts.length;
    let activeQuotes = 0;

    // Calcular valores e contadores baseados nos contratos e adendos
    activeContracts.forEach((contract) => {
      // Contar contratos principais
      contract.signataries.forEach((signatory) => {
        if (['mutuo', 'MUTUO'].includes(contract.type)) {
          p2pContractAmount += Number(signatory.investmentValue);
          p2pContractNumber++;
          return;
        }

        if (['scp', 'SCP'].includes(contract.type)) {
          scpContractAmount += Number(signatory.investmentValue);
          scpContractNumber++;
          uniqueInvestors++;
        }
      });

      // Contar aditivos assinados
      contract.addendum.forEach((addendum) => {
        if (addendum.status === 'FULLY_SIGNED' && this.isAddendumNotExpired(addendum.expiresIn)) {
          totalContracts++;
          if (['mutuo', 'MUTUO'].includes(contract.type)) {
            p2pContractAmount += Number(addendum.value);
            p2pContractNumber++;
            return;
          }
          if (['scp', 'SCP'].includes(contract.type)) {
            scpContractAmount += Number(addendum.value);
            scpContractNumber++;
          }
        }
      });
    });

    // Calcular cotas ativas (SCP dividido por 5000)
    activeQuotes = Math.round(scpContractAmount / 5000);
    return {
      distributedIncome: 0,
      scpWithdraws: 0,
      p2pWithdraws: 0,
      p2pContractNumber,
      scpContractNumber,
      p2pContractAmount,
      scpContractAmount,
      activeQuotes,
      shareholder: uniqueInvestors,
      activeInvestorsNumber: totalContracts,
    };
  }

  private isContractDateValid(startContract: Date, endContract: Date): boolean {
    const currentDate = new Date();
    const startDate = new Date(startContract);
    const endDate = new Date(endContract);

    if (Number.isNaN(startDate.getTime()) || Number.isNaN(endDate.getTime())) {
      return false;
    }

    if (startDate > currentDate) {
      return false;
    }

    if (endDate <= startDate) {
      return false;
    }

    return true;
  }

  private isAddendumNotExpired(expiresIn: string): boolean {
    if (!expiresIn) return false;
    const today = new Date();
    const expires = new Date(expiresIn);
    return expires >= today;
  }
}
