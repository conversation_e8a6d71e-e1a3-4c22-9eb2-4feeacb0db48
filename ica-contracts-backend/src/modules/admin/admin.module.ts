import { Module } from '@nestjs/common';
import { SharedModule } from 'src/shared/shared.module';

import { InvestorModule } from '../investor/investor.module';
import { AdminController } from './controller/admin.controller';
import { AdminDashboardService } from './services/dashboard.service';
import { EditAdminService } from './services/edit-admin.service';
import { GetAdminService } from './services/get-admin.service';
import { GetAdminContractsGrowthChartService } from './services/get-admin-contracts-growth-chart.service';
import { ChangePasswordAdminService } from './services/change-password.service';
import { EmailEntity } from 'src/shared/database/typeorm/entities/email.entity';
import { BusinessEntity } from 'src/shared/database/typeorm/entities/business.entity';
import { OwnerEntity } from 'src/shared/database/typeorm/entities/owner.entity';
import { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EmailNgModule } from 'src/shared/email-ng/email.module';
import { ListContractsAdminService } from './services/list-contracts.service';
@Module({
  imports: [
    SharedModule,
    InvestorModule,
    EmailNgModule,
    TypeOrmModule.forFeature([OwnerEntity, BusinessEntity, EmailEntity, ContractEntity]),
  ],
  providers: [
    GetAdminService,
    AdminDashboardService,
    EditAdminService,
    GetAdminContractsGrowthChartService,
    ChangePasswordAdminService,
    ListContractsAdminService,
  ],
  controllers: [AdminController],
  exports: [],
})
export class AdminModule {}
