import {
  CallH<PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  NestInterceptor,
  BadRequestException,
} from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
import { Observable } from 'rxjs';
import { CreateExistingContractDto } from 'src/modules/account/dto/create-existing-contract.dto';
import {
  getMissingFiles,
  assignFilesToBody,
  flattenValidationErrors,
  fileLabels,
} from './file-upload-validation.helper';

@Injectable()
export class CreateContractFilesValidationInterceptor
  implements NestInterceptor
{
  async intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest();
    const files = request.files;
    const requiredFiles = [
      'contract',
      'proofOfPayment',
      'personalDocument',
      'proofOfResidence',
    ];

    const errors: string[] = [];

    if (files && Object.keys(files).length > 0) {
      const missingFiles = getMissingFiles(files, requiredFiles);
      if (missingFiles.length > 0) {
        errors.push(
          ...missingFiles.map(
            (field) => `${fileLabels[field] ?? field}: Arquivo obrigatório`,
          ),
        );
      }
      assignFilesToBody(files, request.body, requiredFiles);
    } else {
      errors.push(
        ...requiredFiles.map(
          (field) => `${fileLabels[field] ?? field}: Arquivo obrigatório`,
        ),
      );
      requiredFiles.forEach((field) => {
        delete request.body[field];
      });
      delete request.body.companyDocument;
    }

    const dtoData = {
      ...request.body,
      role: request.body.role || '',
      personType: request.body.personType || '',
      contractType: request.body.contractType || '',
      investment: request.body.investment || {},
      bankAccount: request.body.bankAccount || {},
    };

    if (request.body.personType === 'PF') {
      dtoData.individual = request.body.individual || {};
      delete dtoData.company;
    } else if (request.body.personType === 'PJ') {
      dtoData.company = request.body.company || {};
      delete dtoData.individual;
    }

    const dto = plainToInstance(CreateExistingContractDto, dtoData);

    const dtoErrors = await validate(dto, {
      whitelist: true,
      forbidNonWhitelisted: true,
      skipMissingProperties: false,
      validationError: { target: false },
    });

    if (dtoErrors.length > 0) {
      errors.push(...flattenValidationErrors(dtoErrors));
    }

    if (errors.length > 0) {
      throw new BadRequestException(errors);
    }

    request.body = dto;
    return next.handle();
  }
}
