import {
  <PERSON><PERSON><PERSON>,
  <PERSON>ISO<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsS<PERSON>,
  IsUUI<PERSON>,
} from 'class-validator';
import { TransactionMovementTypeEnum } from 'src/shared/enums/transaction-movement-type.enum';

export class IConsultAccountExtract {
  @IsOptional()
  @IsISO8601(
    { strict: true },
    { message: 'A data de início deve estar no formato ISO 8601' },
  )
  DateFrom: string;

  @IsOptional()
  @IsISO8601(
    { strict: true },
    { message: 'A data de início deve estar no formato ISO 8601' },
  )
  DateTo: string;

  @IsOptional()
  @IsString()
  LimitPerPage?: string;

  @IsOptional()
  @IsString()
  Page?: string;

  @IsOptional()
  @IsEnum(TransactionMovementTypeEnum)
  transactionType?: TransactionMovementTypeEnum;

  @IsOptional()
  @IsString()
  @IsUUID()
  id: string;
}
