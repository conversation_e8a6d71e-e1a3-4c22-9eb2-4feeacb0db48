import { IsCPF } from 'brazilian-class-validator';
import { Type } from 'class-transformer';
import {
  IsString,
  IsOptional,
  Length,
  IsLongitude,
  IsLatitude,
  ValidateNested,
  IsBoolean,
  IsUUID,
  IsNotEmpty,
} from 'class-validator';

export class AddressDTO {
  @IsOptional()
  @IsString()
  @Length(5, 8)
  cep?: string;

  @IsOptional()
  @IsString()
  street?: string;

  @IsOptional()
  @IsString()
  number?: string;

  @IsOptional()
  @IsString()
  complement?: string;

  @IsOptional()
  @IsString()
  neighborhood?: string;

  @IsOptional()
  @IsString()
  city?: string;

  @IsOptional()
  @IsString()
  state?: string;

  @IsOptional()
  @IsLongitude()
  longitude?: string;

  @IsOptional()
  @IsLatitude()
  latitude?: string;
}

export class OwnerDto {
  @IsString()
  @IsCPF()
  document: string;

  @IsOptional()
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  phone: string;

  @IsOptional()
  @IsString()
  email: string;

  @IsOptional()
  @IsString()
  motherName: string;

  @IsOptional()
  @IsString()
  dtBirth: Date;

  @IsOptional()
  @IsBoolean()
  pep: boolean;

  @IsOptional()
  @ValidateNested()
  @Type(() => AddressDTO)
  Address: AddressDTO;
}

export class UpdateBusinessDto {
  @IsUUID()
  @IsString()
  @IsNotEmpty()
  businessId: string;

  @IsString()
  @IsOptional()
  accountNumber: string;

  @IsOptional()
  @IsString()
  email?: string;

  @IsOptional()
  @IsString()
  phone?: string;

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => OwnerDto)
  owners: OwnerDto[];

  @IsOptional()
  @ValidateNested()
  @Type(() => AddressDTO)
  Address: AddressDTO;
}
