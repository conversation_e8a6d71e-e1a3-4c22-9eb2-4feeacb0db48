import { Type } from 'class-transformer';
import {
  IsBoolean,
  IsDateString,
  IsDefined,
  IsEmail,
  IsObject,
  IsOptional,
  IsPhoneNumber,
  IsString,
  ValidateNested,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

class Address {
  @IsDefined()
  @IsString()
  cep: string;

  @IsDefined()
  @IsString()
  city: string;

  @IsDefined()
  @IsString()
  state: string;

  @IsDefined()
  @IsString()
  neighborhood: string;

  @IsDefined()
  @IsString()
  street: string;

  @IsOptional()
  @IsString()
  complement?: string;

  @IsDefined()
  @IsString()
  number: string;
}

export class CreateAdminAccountDto {
   @ApiProperty({
    description: 'Data de nascimento do usuário',
  })
  @IsDefined()
  @IsDateString()
  birthDate: Date;


  @ApiProperty({
    description: 'Nome social do usuário',
  })
  @IsDefined()
  @IsString()
  socialName: string;

  @ApiProperty({
    description: 'Taxação do usuário',
  })
  @IsDefined()
  @IsBoolean()
  isTaxable: boolean;

  @ApiProperty({
    description: 'Nome completo do usuário',
  })
  @IsDefined()
  @IsString()
  fullName: string;

  @ApiProperty({
    description: 'CPF do usuário SEM PONTUAÇÃO',
  })
  @IsDefined()
  @IsString()
  cpf: string;

  @ApiProperty({
    description: 'email do usuário',
  })
  @IsDefined()
  @IsString()
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'Número de telefone do usuário',
  })
  @IsDefined()
  @IsString()
  @IsPhoneNumber()
  phoneNumber: string;

  @ApiProperty({
    description: 'Nome da mãe do usuário',
  })
  @IsDefined()
  @IsString()
  motherName: string;

  @ApiProperty({
    description: 'pep',
  })
  @IsDefined()
  @IsBoolean()
  pep: boolean;

  @ApiProperty({
    description: 'Senha do usuário',
  })
  @IsOptional()
  @IsString()
  password?: string;

  @ApiProperty({
    description: 'Endereço do usuário',
  })
  @IsDefined()
  @IsObject()
  @ValidateNested()
  @Type(() => Address)
  address: Address;
}
