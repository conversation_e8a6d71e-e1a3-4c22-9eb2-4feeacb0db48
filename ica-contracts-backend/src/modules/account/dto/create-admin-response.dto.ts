import { ApiProperty } from '@nestjs/swagger';
import { CreateAdminAccountService } from '../services/create-admin-account.service';

type ContractAdmin = Awaited<ReturnType<CreateAdminAccountService['perform']>>;

export class AddendumFileDto {
  @ApiProperty()
  type: string;

  @ApiProperty()
  url: string;
}

export class CreateAdminAccountDto {
   @ApiProperty({
    description: 'Data de nascimento do usuário',
  })
  birthDate: Date;


  @ApiProperty({
    description: 'Nome social do usuário',
  })
  socialName: string;

  @ApiProperty({
    description: 'Taxação do usuário',
  })
  isTaxable: boolean;

  @ApiProperty({
    description: 'Nome completo do usuário',
  })
  fullName: string;

  @ApiProperty({
    description: 'CPF do usuário SEM PONTUAÇÃO',
  })
  cpf: string;

  @ApiProperty({
    description: 'email do usuário',
  })
  email: string;

  @ApiProperty({
    description: 'Número de telefone do usuário',
  })
  phoneNumber: string;

  @ApiProperty({
    description: 'Nome da mãe do usuário',
  })
  motherName: string;

  @ApiProperty({
    description: 'pep',
  })
  pep: boolean;

  @ApiProperty({
    description: 'Senha do usuário',
  })
  password?: string;

  @ApiProperty({
    description: 'Endereço do usuário',
  })
  address: string;
}

export class CreateAdminResponse {
  @ApiProperty({ description: 'ID do usuário criado' })
  id: ContractAdmin['id'];

 
}
