import { Type } from 'class-transformer';
import { IsInt, IsOptional, Max } from 'class-validator';
import { AccountStatusEnum } from 'src/shared/enums/account-status.enum';

export class ListAllAccountsDto {
  @IsOptional()
  status: AccountStatusEnum;

  @IsOptional()
  document: string;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Max(40)
  limit: number;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  page: number;
}
