import { ApiProperty } from '@nestjs/swagger';
import { IsCPFOrCNPJ } from 'brazilian-class-validator';
import { Transform } from 'class-transformer';
import { IsBoolean, IsOptional, IsUUID } from 'class-validator';

export class GetUserInfoDto {
  @ApiProperty({
    description: 'ID do papel/função do usuário',
    example: '123e4567-e89b-12d3-a456-************',
    required: true,
  })
  @IsUUID()
  roleId: string;

  @ApiProperty({
    description: 'CPF ou CNPJ do usuário',
    example: '12345678900',
    required: true,
  })
  @IsCPFOrCNPJ({
    message:
      'CPF ou CNPJ inválido. Use o formato XXXXXXXXXXX (ex: 12345678900 ou 12345678900000)',
  })
  document: string;

  @ApiProperty({
    description:
      'Se true, retorna informações completas do usuário (incluindo endereço e conta bancária). Apenas para CPF.',
    example: true,
    required: false,
    default: true,
  })
  @IsOptional()
  @Transform(({ value }: { value: unknown }) => {
    if (typeof value === 'string') {
      if (value === 'true') return true;
      if (value === 'false') return false;
    }
    return value;
  })
  @IsBoolean({
    message: 'fullInfo deve ser um booleano',
  })
  fullInfo?: boolean = true;
}
