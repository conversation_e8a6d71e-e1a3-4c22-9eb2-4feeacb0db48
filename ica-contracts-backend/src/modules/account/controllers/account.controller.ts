import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpException,
  HttpStatus,
  Inject,
  Param,
  Post,
  Put,
  Query,
  Request,
  UploadedFile,
  UseGuards,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  FileFieldsInterceptor,
  FileInterceptor,
} from '@nestjs/platform-express';
import {
  ApiOperation,
  ApiOkResponse,
  ApiBearerAuth,
  ApiResponse,
  ApiConsumes,
  ApiBody,
  ApiExtraModels,
} from '@nestjs/swagger';
import { Roles } from 'src/shared/decorators/roles.decorator';
import { RolesEnum } from 'src/shared/enums/roles.enum';
import { JwtAuthGuard } from 'src/shared/guards/jwt-auth.guard';
import { RoleGuard } from 'src/shared/guards/role.guard';
import { IRequestUser } from 'src/shared/interfaces/request-user.interface';
import { logger } from 'src/shared/logger';

import { IChangeAccountStatusRequestDto } from '../dto/change-account-status.dto';
import { ChangePasswordDto } from '../dto/change-password.dto';
import { ICloseAccountRequestDto } from '../dto/close-account-request.dto';
import { ConfirmForgotPasswordDto } from '../dto/confirm-forgot-password.dto';
import { IConsultAccountExtract } from '../dto/consult-account-extract.dto';
import { CreateAccountPjDto } from '../dto/create-account-pj.dto';
import { CreateAccountDto } from '../dto/create-account.dto';
import { CreateAdminAccountDto } from '../dto/create-admin-account.dto';
import { CreateAdminResponse } from '../dto/create-admin-response.dto';
import { CreateExistingContractResponse } from '../dto/create-existing-contract-response.dto';
import { CreateExistingContractDto } from '../dto/create-existing-contract.dto';
import { CreatePasswordTransactionDto } from '../dto/create-password-transaction.dto';
import { ForgotPasswordDto } from '../dto/forgot-password.dto';
import { GetAccountDto } from '../dto/get-account.dto';
import { GetAdminBalanceDto } from '../dto/get-admin-balance.dto';
import { GetUserInfoDto } from '../dto/get-user-info.dto';
import { ListAllAccountsDto } from '../dto/list-all-accounts.dto';
import { ResubmitContractDto } from '../dto/resubmit-contract.dto';
import { UpdateAccountPfDTO } from '../dto/update-account-pf.dto';
import { UpdateBusinessDto } from '../dto/update-account-pj.dto';
import { UpdatePasswordTransactionDto } from '../dto/update-password-transaction.dto';
import { CreateContractFilesValidationInterceptor } from '../interceptors/create-contract-files-validation.interceptor';
import { ResubmitContractFilesValidationInterceptor } from '../interceptors/resubmit-contract-files-validation.interceptor';
import {
  BusinessInfoResponseDto,
  UserInfoWithAddressResponseDto,
} from '../response/get-info-response';
import { AccountStatementService } from '../services/account-statement-v2.service';
import { ChangeAccountStatusService } from '../services/change-account-status.service';
import { ChangePasswordService } from '../services/change-password.service';
import { CloseAccountService } from '../services/close-account.service';
import { ConfirmForgotPasswordService } from '../services/confirm-forgot-password.service';
import { ConsultAccountExtractService } from '../services/consult-account-extract.service';
import { CreateAccountPFService } from '../services/create-account-pf.service';
import { CreateAccountPJService } from '../services/create-account-pj.service';
import { CreateAdminAccountService } from '../services/create-admin-account.service';
import { CreateExistingContractService } from '../services/create-existing-contract/create-existing-contract.service';
import { CreatePasswordTransactionService } from '../services/create-password-transaction.service';
import { ForgotPasswordService } from '../services/forgot-password.service';
import { GetAccountService } from '../services/get-account.service';
import { GetAddressService } from '../services/get-address.service';
import { GetBalanceService } from '../services/get-balance.service';
import { GetDocumentsService } from '../services/get-document.service';
import { GetProfileService } from '../services/get-profile.service';
import { GetUserInfoService } from '../services/get-user-info.service';
import { GetVirtualBalanceService } from '../services/get-virtual-balance.service';
import { InvestmentExtractService } from '../services/investment-extract.service';
import { ListAllAccountsService } from '../services/list-all-accouts.service';
import { ListOneAccountService } from '../services/list-one-account.service';
import { ResubmitContractService } from '../services/resubmit-contract/resubmit-contract.service';
import { SendInvestmentExtractService } from '../services/send-investment-extract.service';
import { SendProfileImageService } from '../services/send-profile-image.service';
import { ServiceFeeService } from '../services/service-fee.service';
import { UpdateAccountPFService } from '../services/update-account-pf.service';
import { UpdateAccountPJService } from '../services/update-account-pj.service';
import { UpdatePasswordTransactionService } from '../services/update-password-transaction.service';

@Controller('account')
export class AccountController {
  constructor(
    @Inject(CreateAccountPFService)
    private createAccountService: CreateAccountPFService,
    @Inject(CreateAccountPJService)
    private createAccountPJService: CreateAccountPJService,
    @Inject(GetBalanceService)
    private getBalanceService: GetBalanceService,
    @Inject(GetAccountService)
    private getAccountService: GetAccountService,
    @Inject(ConsultAccountExtractService)
    private consultAccountExtractService: ConsultAccountExtractService,
    @Inject(AccountStatementService)
    private accountStatementService: AccountStatementService,
    @Inject(UpdateAccountPFService)
    private readonly updateAccountPFService: UpdateAccountPFService,
    @Inject(UpdateAccountPJService)
    private readonly updateAccountPJService: UpdateAccountPJService,
    @Inject(CloseAccountService)
    private closeAccountService: CloseAccountService,
    @Inject(ListAllAccountsService)
    private listAllAccountsService: ListAllAccountsService,
    @Inject(ListOneAccountService)
    private listOneAccountsService: ListOneAccountService,
    @Inject(ChangeAccountStatusService)
    private readonly changeAccountStatusService: ChangeAccountStatusService,
    @Inject(GetAddressService)
    private readonly getAddressService: GetAddressService,
    @Inject(InvestmentExtractService)
    private readonly investmentExtractService: InvestmentExtractService,
    @Inject(SendInvestmentExtractService)
    private readonly sendInvestmentExtractService: SendInvestmentExtractService,
    @Inject(ChangePasswordService)
    private readonly changePasswordService: ChangePasswordService,
    private readonly createPasswordTransactionService: CreatePasswordTransactionService,
    @Inject(SendProfileImageService)
    private readonly sendProfileImageService: SendProfileImageService,
    @Inject(ForgotPasswordService)
    private readonly forgotPasswordService: ForgotPasswordService,
    @Inject(GetVirtualBalanceService)
    private readonly getVirtualBalanceService: GetVirtualBalanceService,
    @Inject(ConfirmForgotPasswordService)
    private readonly confirmForgotPasswordService: ConfirmForgotPasswordService,
    @Inject(CreateAdminAccountService)
    private readonly createAccountAdminService: CreateAdminAccountService,
    private serviceFeeService: ServiceFeeService,
    private readonly updatePasswordTransactionService: UpdatePasswordTransactionService,
    private readonly getDocumentsService: GetDocumentsService,
    private readonly createExistingContractService: CreateExistingContractService,
    private readonly getProfileService: GetProfileService,
    private readonly resubmitContractService: ResubmitContractService,
    private readonly getUserInfoService: GetUserInfoService,
  ) {}

  @Post('create/pf')
  @UseGuards(JwtAuthGuard)
  @Roles(
    RolesEnum.SUPERADMIN,
    RolesEnum.ADMIN,
    RolesEnum.BROKER,
    RolesEnum.ADVISOR,
  )
  async createPF(
    @Body()
    body: CreateAccountDto,
  ) {
    try {
      const response = await this.createAccountService.perform(body);
      return response;
    } catch (error) {
      throw new HttpException(
        { error: error.response || error.message },
        error.status || 400,
      );
    }
  }

  @Post('create/pj')
  @UseGuards(JwtAuthGuard)
  @Roles(
    RolesEnum.SUPERADMIN,
    RolesEnum.ADMIN,
    RolesEnum.BROKER,
    RolesEnum.ADVISOR,
  )
  async createPJ(
    @Body()
    body: CreateAccountPjDto,
  ) {
    try {
      const response = await this.createAccountPJService.perform(body);
      return response;
    } catch (error) {
      throw new HttpException(
        { error: error.response || error.message },
        error.status,
      );
    }
  }

  @Get('balance')
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.BROKER, RolesEnum.ADVISOR, RolesEnum.INVESTOR)
  async getBalance(
    @Request()
    request: IRequestUser,
  ) {
    return this.getBalanceService.perform({ userId: request.user.id });
  }

  @Get('admin/balance')
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.ADMIN, RolesEnum.SUPERADMIN)
  async getAdminBalance(
    @Query()
    query: GetAdminBalanceDto,
  ) {
    try {
      return this.getBalanceService.perform({ userId: query.accountId });
    } catch (error) {
      throw new HttpException(
        { error: error.response || error.message },
        error.status,
      );
    }
  }

  @Get('extract')
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.BROKER, RolesEnum.ADVISOR, RolesEnum.INVESTOR)
  async getExtract(
    @Request()
    request: IRequestUser,
    @Query() queryParams: IConsultAccountExtract,
  ) {
    try {
      const response = await this.consultAccountExtractService.execute(
        queryParams,
        request.user.id,
      );
      return response;
    } catch (error) {
      throw new HttpException(
        { error: error.response || error.message },
        error.status,
      );
    }
  }

  @Get('statement')
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.BROKER, RolesEnum.ADVISOR, RolesEnum.INVESTOR)
  async getStatement(
    @Request()
    request: IRequestUser,
    @Query() queryParams: IConsultAccountExtract,
  ) {
    try {
      const response = await this.accountStatementService.execute(
        {
          ...queryParams,
        },
        request.user.id,
      );
      return response;
    } catch (error) {
      throw new HttpException(
        { error: error.response || error.message },
        error.status,
      );
    }
  }

  @Get('admin/extract')
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.ADMIN, RolesEnum.SUPERADMIN)
  async getAdminExtract(@Query() queryParams: IConsultAccountExtract) {
    try {
      const response = await this.accountStatementService.execute(
        queryParams,
        queryParams.id,
      );
      return response;
    } catch (error) {
      throw new HttpException(
        { error: error.response || error.message },
        error.status,
      );
    }
  }

  @Put('updatePf')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.BROKER, RolesEnum.ADVISOR, RolesEnum.INVESTOR)
  async updateAccountPF(
    @Request()
    request: IRequestUser,
    @Body() data: UpdateAccountPfDTO,
  ) {
    try {
      await this.updateAccountPFService.perform(data);
      return { message: 'Conta atualizada com sucesso.' };
    } catch (err) {
      logger.error(`AccountController.updateAccountPF() -> ${err}`);
      throw err;
    }
  }

  @Put('updatePj')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.BROKER, RolesEnum.ADVISOR, RolesEnum.INVESTOR)
  async updateAccountPJ(
    @Request()
    request: IRequestUser,
    @Body() data: UpdateBusinessDto,
  ) {
    try {
      await this.updateAccountPJService.perform({
        ...data,
        businessId: request.user.id,
      });
      return { message: 'Conta atualizada com sucesso.' };
    } catch (err) {
      logger.error(`AccountController.updateAccountPJ() -> ${err}`);
      throw err;
    }
  }

  @Delete('close')
  @HttpCode(HttpStatus.NO_CONTENT)
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.ADMIN, RolesEnum.SUPERADMIN)
  async closeAccount(
    @Body()
    body: ICloseAccountRequestDto,
  ) {
    try {
      await this.closeAccountService.execute(body);
    } catch (error) {
      throw new HttpException(
        { error: error.response || error.message },
        error.status || 400,
      );
    }
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  async getAccount(
    @Request()
    request: IRequestUser,
  ) {
    try {
      const data = await this.getAccountService.perform(request.user.id);
      return data;
    } catch (error) {
      throw new HttpException(
        { error: error.response || error.message },
        error.status,
      );
    }
  }
  @Get('profile')
  @UseGuards(JwtAuthGuard)
  async getProfile(
    @Request()
    request: IRequestUser,
  ) {
    try {
      const data = await this.getProfileService.perform(request.user.id);
      return data;
    } catch (error) {
      throw new HttpException(
        { error: error.response || error.message },
        error.status,
      );
    }
  }

  @Put('change-status')
  @HttpCode(HttpStatus.NO_CONTENT)
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.BROKER, RolesEnum.ADVISOR, RolesEnum.INVESTOR)
  async changeAccountStatus(
    @Request()
    request: IRequestUser,
    @Body()
    body: IChangeAccountStatusRequestDto,
  ) {
    try {
      await this.changeAccountStatusService.execute(
        { reason: body.reason, status: body.status },
        request.user.id,
      );
    } catch (error) {
      throw new HttpException(
        { error: error.response || error.message },
        error.status,
      );
    }
  }

  @Put('change-password')
  @UseGuards(JwtAuthGuard)
  async changePassword(
    @Request()
    request: IRequestUser,
    @Body()
    body: ChangePasswordDto,
  ) {
    try {
      await this.changePasswordService.perform(body, request.user.id);
    } catch (error) {
      throw new HttpException(
        { error: error.response || error.message },
        error.status,
      );
    }
  }

  @Post('transaction-password')
  @UseGuards(JwtAuthGuard)
  async transactionPassword(
    @Request()
    request: IRequestUser,
    @Body()
    body: CreatePasswordTransactionDto,
  ): Promise<void> {
    await this.createPasswordTransactionService.perform(request.user.id, body);
  }

  @Put('transaction-password')
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.BROKER, RolesEnum.ADVISOR, RolesEnum.INVESTOR)
  async updateTransactionPassword(
    @Request()
    request: IRequestUser,
    @Body()
    body: UpdatePasswordTransactionDto,
  ): Promise<void> {
    await this.updatePasswordTransactionService.perform(request.user.id, body);
  }

  @Get('get-address')
  @UseGuards(JwtAuthGuard, RoleGuard)
  async getAddress(
    @Request()
    request: IRequestUser,
  ): Promise<void> {
    return this.getAddressService.execute(request.user.id);
  }

  @Post('profile/image')
  @UseInterceptors(FileInterceptor('file'))
  @UseGuards(JwtAuthGuard)
  async sendProfileImage(
    @Request() request: IRequestUser,
    @Body() body: any,
    @UploadedFile()
    file: Express.Multer.File,
  ) {
    try {
      await this.sendProfileImageService.perform(request.user.id, file);
    } catch (error) {
      throw new HttpException(
        { error: error.response || error.message },
        error.status,
      );
    }
  }

  @Get('service-fee/:accountId')
  @UseGuards(JwtAuthGuard)
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.BROKER, RolesEnum.ADVISOR, RolesEnum.INVESTOR)
  async performServiceFee(
    @Request()
    request: IRequestUser,
  ) {
    const result = await this.serviceFeeService.perform(request.user.id);
    return result;
  }

  @Get('list-all')
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.SUPERADMIN, RolesEnum.ADMIN)
  async listAll(
    @Query()
    query: ListAllAccountsDto,
  ) {
    const result = await this.listAllAccountsService.execute(query);
    return result;
  }

  @Get('admin/list-one')
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.SUPERADMIN, RolesEnum.ADMIN)
  async listOne(
    @Query()
    query: GetAccountDto,
  ) {
    const result = await this.listOneAccountsService.perform(query.id);
    return result;
  }

  @Get('investment-info')
  @UseGuards(JwtAuthGuard)
  @UseGuards(JwtAuthGuard, RoleGuard)
  async getInvestmentExtract(
    @Request()
    request: IRequestUser,
  ) {
    return this.investmentExtractService.execute(request.user.id);
  }

  @Put('investment-report/:month')
  @UseGuards(JwtAuthGuard)
  @UseGuards(JwtAuthGuard, RoleGuard)
  async sendInvestmentReport(
    @Request()
    request: IRequestUser,
    @Param('month') month: number,
  ) {
    return this.sendInvestmentExtractService.execute(request.user.id, month);
  }

  @Get('forgot-password')
  async forgotPassword(
    @Query()
    query: ForgotPasswordDto,
  ) {
    try {
      return this.forgotPasswordService.perform(query);
    } catch (error) {
      throw new HttpException(
        { error: error.response.data || error.message },
        error.status,
      );
    }
  }

  @Post('forgot-password')
  async confirmForgotPassword(
    @Body()
    body: ConfirmForgotPasswordDto,
  ) {
    try {
      return this.confirmForgotPasswordService.perform(body);
    } catch (error) {
      throw new HttpException(
        { error: error.response.data || error.message },
        error.status,
      );
    }
  }

  @Get('document/:accountId')
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.SUPERADMIN, RolesEnum.ADMIN)
  async getDocument(@Param('accountId') accountId: string) {
    const document = await this.getDocumentsService.perform(accountId);
    return document;
  }

  @Get('virtual-balance/:accountId')
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.SUPERADMIN, RolesEnum.ADMIN)
  async getVirtualBalance(@Param('accountId') accountId: string) {
    const output = await this.getVirtualBalanceService.perform({
      userId: accountId,
    });
    return output;
  }

  @Post('create/existing-contract')
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(
    RolesEnum.SUPERADMIN,
    RolesEnum.ADMIN,
    RolesEnum.BROKER,
    RolesEnum.ADVISOR,
  )
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'contract', maxCount: 1 },
      { name: 'proofOfPayment', maxCount: 1 },
      { name: 'personalDocument', maxCount: 1 },
      { name: 'proofOfResidence', maxCount: 1 },
      { name: 'companyDocument', maxCount: 1 },
    ]),
    CreateContractFilesValidationInterceptor,
  )
  @HttpCode(HttpStatus.CREATED)
  // ** Docs **
  @ApiOperation({
    summary: 'Rota para criar um contrato para um usuário(PF ou PJ)',
    description:
      'Cria um contrato para um usuário(PF ou PJ) com base nos dados fornecidos.',
  })
  @ApiConsumes('multipart/form-data', 'application/json')
  @ApiBody({ type: CreateExistingContractDto })
  @ApiOkResponse({
    description: 'Contrato criado com sucesso',
    type: CreateExistingContractResponse,
  })
  @ApiResponse({ status: 400, description: 'Requisição inválida.' })
  @ApiResponse({ status: 401, description: 'Não autorizado.' })
  @ApiBearerAuth('Bearer')
  async createExistingContract(
    @Body() createExistingContractDto: CreateExistingContractDto,
    @Request() request: IRequestUser,
  ) {
    try {
      return await this.createExistingContractService.perform(
        createExistingContractDto,
        request.user.id,
      );
    } catch (error) {
      console.error(error);
      throw new HttpException(
        { error: error.response?.data || error.message },
        error.status ||
          error.response?.statusCode ||
          HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('create/admin')
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.SUPERADMIN)
  // ** Docs **
  @ApiOperation({
    summary: 'Rota para criar uma conta de administrador',
    description: 'Retorna o ID do usuário administrador criado.',
  })
  @ApiOkResponse({
    description: 'Usuário administrador criado com sucesso.',
    type: CreateAdminResponse,
  })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiBearerAuth('Bearer')
  async createAccountAdmin(
    @Body()
    body: CreateAdminAccountDto,
  ) {
    try {
      return this.createAccountAdminService.perform(body);
    } catch (error) {
      throw new HttpException(
        { error: error.response.data || error.message },
        error.status,
      );
    }
  }

  @Put('resubmit-contract/')
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.BROKER, RolesEnum.ADVISOR)
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'contract', maxCount: 1 },
      { name: 'proofOfPayment', maxCount: 1 },
      { name: 'personalDocument', maxCount: 1 },
      { name: 'proofOfResidence', maxCount: 1 },
    ]),
    ResubmitContractFilesValidationInterceptor,
  )
  async resubmitContract(@Body() data: ResubmitContractDto) {
    try {
      return await this.resubmitContractService.perform(data);
    } catch (error) {
      throw new HttpException(
        { error: error.response?.data || error.message },
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('get-user-info')
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.SUPERADMIN, RolesEnum.BROKER, RolesEnum.ADVISOR)
  @UsePipes(
    new ValidationPipe({
      whitelist: true,
    }),
  )
  @ApiOperation({
    summary: 'Rota para verificar informações de um usuário',
    description:
      'Retorna as informações de um usuário baseado no CPF ou CNPJ fornecido. Se roleId for fornecido, retorna informações específicas do papel do usuário. Para CPF, sempre retorna endereço. Se fullInfo for true (padrão), também retorna conta bancária. Para CNPJ, retorna informações da empresa incluindo endereço e representante.',
  })
  @ApiExtraModels(UserInfoWithAddressResponseDto, BusinessInfoResponseDto)
  @ApiResponse({
    status: 200,
    description: 'Informações do usuário retornadas com sucesso.',
    schema: {
      oneOf: [
        { $ref: '#/components/schemas/UserInfoWithAddressResponseDto' },
        { $ref: '#/components/schemas/BusinessInfoResponseDto' },
      ],
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Documento inválido ou parâmetros ausentes.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 404, description: 'Usuário não encontrado.' })
  @ApiBearerAuth('Bearer')
  async getUserInfo(
    @Query() query: GetUserInfoDto,
    @Request() request: IRequestUser,
  ): Promise<UserInfoWithAddressResponseDto | BusinessInfoResponseDto> {
    return await this.getUserInfoService.getUserInfo(
      query.document,
      query.roleId,
      query.fullInfo,
      request.user.id,
    );
  }
}
