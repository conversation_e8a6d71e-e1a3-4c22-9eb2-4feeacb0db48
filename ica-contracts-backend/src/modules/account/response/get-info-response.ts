import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

// Base interfaces for TypeScript type checking
export interface IAddressResponse {
  id: string;
  cep: string;
  street: string;
  neighborhood: string;
  number: string;
  city: string;
  state: string;
  complement?: string;
}

export interface IBankAccountResponse {
  id: string;
  bank: string;
  number: string;
  branch: string;
  type: string;
  status: string;
}

export interface IBusinessInfoResponse {
  type: 'business';
  companyName: string;
  fantasyName: string;
  cnpj: string;
  businessType: string;
  size: string;
  email: string;
  dtOpening: Date;
  address?: IAddressResponse;
  representative?: {
    type: 'owner';
    id: string;
    name: string;
    cpf: string;
    email: string;
    phone: string;
    occupation: string;
    birthDate: Date;
    nationality: string;
    rg: string;
    rgIssuingAgency: string;
    pep: boolean;
    motherName: string;
    address?: IAddressResponse;
  };
  bankAccount?: IBankAccountResponse;
}

export interface IUserResponse {
  type: 'owner';
  id: string;
  name: string;
  cpf: string;
  email: string;
  phone: string;
  occupation?: string;
  birthDate: Date;
  nationality?: string;
  rg?: string;
  rgIssuingAgency?: string;
  pep: boolean;
  motherName: string;
  address?: IAddressResponse;
  bankAccount?: IBankAccountResponse;
}

// DTOs with Swagger decorators for API documentation
export class AddressResponseDto implements IAddressResponse {
  @ApiProperty({ description: 'ID do endereço' })
  id: string;

  @ApiProperty({ description: 'CEP do endereço' })
  cep: string;

  @ApiProperty({ description: 'Rua do endereço' })
  street: string;

  @ApiProperty({ description: 'Bairro do endereço' })
  neighborhood: string;

  @ApiProperty({ description: 'Número do endereço' })
  number: string;

  @ApiProperty({ description: 'Cidade do endereço' })
  city: string;

  @ApiProperty({ description: 'Estado do endereço' })
  state: string;

  @ApiProperty({ description: 'Complemento do endereço', required: false })
  complement?: string;
}

export class BankAccountResponseDto implements IBankAccountResponse {
  @ApiProperty({ description: 'ID da conta bancária' })
  id: string;

  @ApiProperty({ description: 'Nome do banco' })
  bank: string;

  @ApiProperty({ description: 'Número da conta' })
  number: string;

  @ApiProperty({ description: 'Agência bancária' })
  branch: string;

  @ApiProperty({ description: 'Tipo da conta' })
  type: string;

  @ApiProperty({ description: 'Status da conta' })
  status: string;
}

export class UserInfoWithAddressResponseDto implements IUserResponse {
  @ApiProperty({ description: 'Tipo do usuário', enum: ['owner'] })
  type: 'owner';

  @ApiProperty({ description: 'ID do usuário' })
  id: string;

  @ApiProperty({ description: 'Nome do usuário' })
  name: string;

  @ApiProperty({ description: 'CPF do usuário' })
  cpf: string;

  @ApiProperty({ description: 'Email do usuário' })
  email: string;

  @ApiProperty({ description: 'Telefone do usuário' })
  phone: string;

  @ApiProperty({ description: 'Ocupação do usuário', required: false })
  occupation?: string;

  @ApiProperty({ description: 'Data de nascimento do usuário' })
  @Type(() => Date)
  birthDate: Date;

  @ApiProperty({ description: 'Nacionalidade do usuário', required: false })
  nationality?: string;

  @ApiProperty({ description: 'RG do usuário', required: false })
  rg?: string;

  @ApiProperty({ description: 'Órgão emissor do RG', required: false })
  rgIssuingAgency?: string;

  @ApiProperty({ description: 'Indica se o usuário é PEP' })
  pep: boolean;

  @ApiProperty({ description: 'Nome da mãe do usuário' })
  motherName: string;

  @ApiProperty({ type: AddressResponseDto, required: false })
  address?: AddressResponseDto;

  @ApiProperty({ type: BankAccountResponseDto, required: false })
  bankAccount?: BankAccountResponseDto;
}

export class BusinessInfoResponseDto implements IBusinessInfoResponse {
  @ApiProperty({ example: 'business' })
  type: 'business';

  @ApiProperty({ example: 'Empresa XYZ Ltda' })
  companyName: string;

  @ApiProperty({ example: 'XYZ' })
  fantasyName: string;

  @ApiProperty({ example: '*********00000' })
  cnpj: string;

  @ApiProperty({ example: 'LTDA' })
  businessType: string;

  @ApiProperty({ example: 'MEDIUM' })
  size: string;

  @ApiProperty({ example: '<EMAIL>' })
  email: string;

  @ApiProperty({ example: '2020-01-01' })
  dtOpening: Date;

  @ApiProperty({ type: () => AddressResponseDto, required: false })
  address?: IAddressResponse;

  @ApiProperty({
    type: () => Object,
    required: false,
    example: {
      type: 'owner',
      id: '123e4567-e89b-12d3-a456-************',
      name: 'John Doe',
      cpf: '*********00',
      email: '<EMAIL>',
      phone: '***********',
      occupation: 'CEO',
      birthDate: '1980-01-01',
      nationality: 'Brazilian',
      rg: '*********',
      rgIssuingAgency: 'SSP',
      pep: false,
      motherName: 'Jane Doe',
      address: {
        id: '123e4567-e89b-12d3-a456-************',
        cep: '12345678',
        street: 'Main St',
        neighborhood: 'Downtown',
        number: '123',
        city: 'São Paulo',
        state: 'SP',
        complement: 'Suite 1',
      },
    },
  })
  representative?: {
    type: 'owner';
    id: string;
    name: string;
    cpf: string;
    email: string;
    phone: string;
    occupation: string;
    birthDate: Date;
    nationality: string;
    rg: string;
    rgIssuingAgency: string;
    pep: boolean;
    motherName: string;
    address?: IAddressResponse;
  };

  @ApiProperty({ type: () => BankAccountResponseDto, required: false })
  bankAccount?: IBankAccountResponse;
}
