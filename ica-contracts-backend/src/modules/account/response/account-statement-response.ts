export const TransactionType = {
  CREDIT: 'CREDIT',
  DEBIT: 'DEBIT',
} as const;

export type TransactionParty = {
  account: string;
  branch: string;
  taxId: string;
  name: string;
  accountType: string;
  bank?: string;
};

export type TransactionMovement = {
  id: string;
  clientCode: string;
  type: keyof typeof TransactionType;
  description: string;
  transactionDate: string;
  status: string;
  amount: number;
  creditParty: TransactionParty;
  debitParty: TransactionParty;
};

export interface IAccountStatementResponse {
  movements: Array<TransactionMovement>;
  totalItems: number;
  totalPages: number;
  currentPage: number;
}
