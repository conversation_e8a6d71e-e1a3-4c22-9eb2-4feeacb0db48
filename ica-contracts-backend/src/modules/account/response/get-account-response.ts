import { AccountStatusEnum } from 'src/shared/enums/account-status.enum';
import { ServiceFeeChargeStatusEnum } from 'src/shared/enums/service-fee-charge-status.enum';

interface IOwner {
  name: string;
  cpf: string;
  email: string;
  phone: string;
  motherName: string;
  dtBirth: Date;
  nickname: string;
  updatedAt: Date;
  createdAt: Date;
  deletedAt: Date;
  pep: string;
}

interface IBusiness {
  companyName: string;
  fantasyName: string;
  cnpj: string;
  type: string;
  size: string;
  email: string;
  dtOpening: Date;
  updatedAt: Date;
  createdAt: Date;
  deletedAt: Date;
  owner?: IOwner;
}

export interface IGetAccountResponse {
  id: string;
  externalId: string;
  account: {
    number: string;
    branch: string;
    transactionPasswordExists: boolean;
    chargeStatus?: ServiceFeeChargeStatusEnum;
    totalDebt?: number;
  };
  type: string;
  owner?: IOwner;
  business?: IBusiness;
  status: AccountStatusEnum;
  temporaryPassword: boolean;
  profileImage: string;
  roles: Array<{ name: string; roleId: string }>;
  prefeitura: {
    totalInvested: number;
    yield: number;
    startContract: Date;
    endContract: Date;
    profitability: number;
    investors: number;
    totalApplied: number;
    statesData: any;
  };
  twoFactorSecret?: string;
}
