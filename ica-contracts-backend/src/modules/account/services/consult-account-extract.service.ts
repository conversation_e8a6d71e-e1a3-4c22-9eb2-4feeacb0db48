import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { endOfDay, startOfDay } from 'date-fns';
import { Movement } from 'src/apis/celcoin/responses/consult-extract-celcoin.response';
import { ConsultAccountExtractCelcoinService } from 'src/apis/celcoin/services/consult-extract-celcoin.service';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { RechargeEntity } from 'src/shared/database/typeorm/entities/recharge.entity';
import { TransactionEntity } from 'src/shared/database/typeorm/entities/transaction.entity';
import { Between, Equal, Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';

import { IConsultAccountExtract } from '../dto/consult-account-extract.dto';

@Injectable()
export class ConsultAccountExtractService {
  constructor(
    @InjectRepository(AccountEntity)
    private accountDb: Repository<AccountEntity>,
    @InjectRepository(RechargeEntity)
    private rechargeDb: Repository<RechargeEntity>,
    @InjectRepository(TransactionEntity)
    private transactionRepository: Repository<TransactionEntity>,
    @Inject(ConsultAccountExtractCelcoinService)
    private apiCelcoin: ConsultAccountExtractCelcoinService,
  ) {}

  async execute(data: IConsultAccountExtract, id: string) {
    const account = await this.accountDb.findOne({
      relations: {
        owner: true,
        document: true,
        business: {
          ownerBusinessRelation: {
            owner: true,
          },
        },
      },
      where: [
        {
          ownerId: Equal(id),
        },
        {
          businessId: Equal(id),
        },
        {
          id: Equal(id),
        },
      ],
    });

    if (!account) throw new NotFoundException('Conta nao encontrada');

    const page = data.Page ? data.Page : String(1);
    const limit = data.LimitPerPage ? data.LimitPerPage : String(1);

    const document =
      account.type === 'physical' ? account.owner.cpf : account.business.cnpj;
    const {
      body: accountExtract,
      totalItems,
      totalPages,
    } = await this.apiCelcoin.consultExtract({
      Account: account.number,
      DocumentNumber: document,
      DateFrom: data.DateFrom,
      DateTo: data.DateTo,
      LimitPerPage: limit,
      Page: page,
    });

    const shouldFilter = !!data.transactionType;

    if (shouldFilter && Array.isArray(accountExtract.movements)) {
      const movs = accountExtract.movements as Movement[];
      accountExtract.movements = movs.filter((movement) =>
        movement.movementType.includes(data.transactionType as string),
      );
    }

    const investmentInfo = account.owner ? account.owner : account.business;

    let profitabilityFee: number;
    let profitabilityValue: number;
    if (
      investmentInfo.startContract &&
      investmentInfo.endContract &&
      investmentInfo.yield &&
      investmentInfo.totalInvested
    ) {
      profitabilityFee = investmentInfo.yield / 12;

      profitabilityValue =
        investmentInfo.totalInvested * (profitabilityFee / 100);
    }

    let transactions;

    const accountResponse = { data: [] };
    let manualRemoveList;
    accountResponse.data.forEach((account) => {
      if (document === account.accountDocument) {
        manualRemoveList = account.manualRemove;
        transactions = account.transactions;
      }
    });

    const minDate = new Date(data.DateFrom);
    const maxDate = new Date(data.DateTo);

    if (transactions) {
      transactions = transactions.filter(
        (transaction) =>
          new Date(transaction.date) >= minDate &&
          new Date(transaction.date) <= maxDate,
      );

      transactions.sort(
        (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime(),
      );
    }

    const movements = await Promise.all(
      accountExtract.movements.map(async (mov: Movement) => {
        let transactionInfo = null;

        const transaction = await this.transactionRepository.findOne({
          where: [{ code: mov.clientCode }, { code: mov.id }],
        });

        if (!transaction) return mov;

        if (transaction.transferMetadata) {
          transactionInfo = JSON.parse(transaction.transferMetadata);
        }

        if (!transactionInfo) {
          return null;
        }

        if (
          transactionInfo.debitParty?.taxId === '***********' ||
          transactionInfo.debitParty?.taxId === '**************'
        ) {
          return null; // Return null to filter out this movement
        }

        if (
          manualRemoveList &&
          Array.isArray(manualRemoveList) &&
          manualRemoveList.includes(mov.id)
        ) {
          return null; // Return null to filter out this movement
        }

        if (profitabilityValue && mov.amount === profitabilityValue) {
          Object.defineProperty(mov, 'description', 'rendimento');
        }
        let injectedTransactions = [];
        if (transactions) {
          const dateTransactions = transactions.filter(
            (transaction) => transaction.date === mov.createDate.split('T')[0],
          );

          injectedTransactions = dateTransactions.map((transaction) => ({
            mov: {
              id: uuidv4(),
              clientCode: account.id,
              description: 'P2P',
              createDate: `${transaction.date}T00:00:00`,
              lastUpdateDate: `${transaction.date}T00:00:00`,
              amount: transaction.amount,
              status: 'Saldo Liberado',
              balanceType: transaction.type === 'CREDIT' ? 'CREDIT' : 'DEBIT',
              movementType:
                transaction.type === 'CREDIT'
                  ? 'PIXPAYMENTIN'
                  : 'PIXPAYMENTOUT',
            },
            creditParty:
              transaction.type === 'CREDIT'
                ? transactionInfo?.creditParty || {}
                : {
                    account: '*************',
                    branch: '0001',
                    taxId: '**************',
                    name: 'ICABANK',
                    accountType: 'TRAN',
                    bank: '********',
                  },
            debitParty:
              transaction.type === 'DEBIT'
                ? transactionInfo?.debitParty || {}
                : {
                    account: '*************',
                    branch: '0001',
                    taxId: '**************',
                    name: 'ICABANK',
                    accountType: 'TRAN',
                    bank: '********',
                  },
          }));

          transactions = transactions.filter(
            (transaction) => !dateTransactions.includes(transaction),
          );
        }
        return [
          {
            ...mov,
            creditParty: transactionInfo?.creditParty || {},
            debitParty: transactionInfo?.debitParty || {},
          },
          ...injectedTransactions,
        ];
      }),
    );

    const recharges = await this.rechargeDb.find({
      where: {
        accountId: account.id,
        createdAt: Between(startOfDay(data.DateFrom), endOfDay(data.DateTo)),
      },
    });

    let remainingTransactions = [];
    let filteredMovements = movements.flat().filter((mov) => mov !== null);

    if (recharges.length > 0 && filteredMovements.length > 0) {
      const rechargeIds = new Set(
        recharges.map((recharge) => recharge.externalId),
      );

      filteredMovements = filteredMovements.filter(
        (moviment) => !rechargeIds.has(moviment.id),
      );
    }
    if (transactions) {
      remainingTransactions = transactions.map((transaction) => ({
        mov: {
          id: uuidv4(),
          clientCode: account.id,
          description: 'P2P',
          createDate: `${transaction.date}T00:00:00`,
          lastUpdateDate: `${transaction.date}T00:00:00`,
          amount: transaction.amount,
          status: 'Saldo Liberado',
          balanceType: transaction.type === 'CREDIT' ? 'CREDIT' : 'DEBIT',
          movementType:
            transaction.type === 'CREDIT' ? 'PIXPAYMENTIN' : 'PIXPAYMENTOUT',
        },
        creditParty:
          transaction.type === 'DEBIT'
            ? {
                account: '*************',
                branch: '0001',
                taxId: '**************',
                name: 'ICABANK',
                accountType: 'TRAN',
                bank: '********',
              }
            : {
                account: account.number,
                branch: '0001',
                taxId:
                  account.type === 'business'
                    ? account.business.cnpj
                    : account.owner.cpf,
                name:
                  account.type === 'business'
                    ? account.business.companyName
                    : account.owner.name,
                accountType: 'TRAN',
                bank: '********',
              },
        debitParty:
          transaction.type === 'CREDIT'
            ? {
                account: '*************',
                branch: '0001',
                taxId: '**************',
                name: 'ICABANK',
                accountType: 'TRAN',
                bank: '********',
              }
            : {
                account: account.number,
                branch: '0001',
                taxId:
                  account.type === 'business'
                    ? account.business.cnpj
                    : account.owner.cpf,
                name:
                  account.type === 'business'
                    ? account.business.companyName
                    : account.owner.name,
                accountType: 'TRAN',
                bank: '********',
              },
      }));
    }
    return {
      movements: [...remainingTransactions, ...filteredMovements],
      totalItems,
      totalPages,
      currentPage: page,
    };
  }
}
