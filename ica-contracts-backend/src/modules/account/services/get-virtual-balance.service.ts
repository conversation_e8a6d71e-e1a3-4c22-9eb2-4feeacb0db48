import { Inject, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { BalanceCelcoinService } from 'src/apis/celcoin/services/balance-celcoin.service';
import { VirtualBalanceService } from 'src/apis/icainvest-credit/services/virtual-balance.service';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { Equal, Repository } from 'typeorm';

import { GetBalanceDto } from '../dto/get-balance.dto';

export class GetVirtualBalanceService {
  constructor(
    @InjectRepository(AccountEntity)
    private accountDb: Repository<AccountEntity>,
    @Inject(VirtualBalanceService)
    private virtualBalanceService: VirtualBalanceService,
    @Inject(BalanceCelcoinService)
    private balanceCelcoin: BalanceCelcoinService,
  ) {}

  async perform(input: GetBalanceDto) {
    const account = await this.accountDb.findOne({
      relations: {
        owner: true,
        document: true,
        business: {
          ownerBusinessRelation: {
            owner: true,
          },
        },
      },
      where: [
        {
          ownerId: Equal(input.userId),
        },
        {
          businessId: Equal(input.userId),
        },
        {
          id: Equal(input.userId),
        },
      ],
    });
    if (!account) {
      throw new NotFoundException('Usuário não encontrado.');
    }

    const virtualBalanceResponse =
      await this.virtualBalanceService.checkBalance({
        accountId: account.id,
      });

    const document =
      account.type === 'physical' ? account.owner.cpf : account.business.cnpj;

    const response = await this.balanceCelcoin.getBalance({
      DocumentNumber: document,
      Account: account.number,
    });

    const virtualBalanceAmount = virtualBalanceResponse.amount;
    const balance = response.body.amount;
    return {
      virtualBalance: virtualBalanceAmount,
      balance,
    };
  }
}
