import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { IGetBalanceApiResponse } from 'src/apis/celcoin/responses/get-balance-celcoin.response';
import { BalanceCelcoinService } from 'src/apis/celcoin/services/balance-celcoin.service';
import { PixTransactionCelcoinService } from 'src/apis/celcoin/services/pix-transaction-celcoin.service';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { ServiceFeeEntity } from 'src/shared/database/typeorm/entities/service-fee.entity';
import { ServiceFeeChargeStatusEnum } from 'src/shared/enums/service-fee-charge-status.enum';
import { ServiceFeePaymentStatusEnum } from 'src/shared/enums/service-fee-payment-status.enum';
import { logger } from 'src/shared/logger';
import { Repository, Equal, MoreThanOrEqual } from 'typeorm';
import { v4 } from 'uuid';

@Injectable()
export class ServiceFeeService {
  constructor(
    @InjectRepository(AccountEntity)
    private accountDb: Repository<AccountEntity>,
    @InjectRepository(ServiceFeeEntity)
    private serviceFeeRepository: Repository<ServiceFeeEntity>,
    private balanceService: BalanceCelcoinService,
    private celcoinService: PixTransactionCelcoinService,
  ) {}

  async perform(id: string) {
    const account = await this.accountDb.findOne({
      relations: {
        owner: true,
        document: true,
        business: {
          ownerBusinessRelation: {
            owner: true,
          },
        },
      },
      where: [
        {
          ownerId: Equal(id),
        },
        {
          businessId: Equal(id),
        },
        {
          id: Equal(id),
        },
      ],
    });

    if (!account) {
      return;
    }

    const date = new Date();
    const currentMonth = new Date(date.getFullYear(), date.getMonth(), 1);

    const serviceFee = await this.serviceFeeRepository.findOne({
      where: {
        account: { id: account.id },
        created_at: MoreThanOrEqual(currentMonth),
      },
      lock: {
        mode: 'pessimistic_write',
        onLocked: 'skip_locked',
      },
    });

    let paymentSuccess = false;
    let chargeStatus = ServiceFeeChargeStatusEnum.NO_CHARGE_NEEDED;

    if (!serviceFee) {
      paymentSuccess = await this.createMonthlyBillingRecord(account);
    } else if (
      serviceFee.payment_status === ServiceFeePaymentStatusEnum.PENDING
    ) {
      paymentSuccess = await this.attemptPayment(account, serviceFee);
    }

    if (paymentSuccess) {
      chargeStatus = ServiceFeeChargeStatusEnum.CORRECTLY_CHARGED;
    } else {
      const totalDebt = await this.calculateTotalDebt(account.id);
      if (totalDebt > 0) {
        chargeStatus = ServiceFeeChargeStatusEnum.INSUFFICIENT_BALANCE;
      }
    }

    const totalDebt = await this.calculateTotalDebt(account.id);

    return { chargeStatus, totalDebt };
  }

  private async calculateTotalDebt(accountId: string): Promise<number> {
    const pendingFees = await this.serviceFeeRepository.find({
      where: {
        account: { id: accountId },
        payment_status: ServiceFeePaymentStatusEnum.PENDING,
      },
    });

    const totalDebt = pendingFees.reduce(
      (acc, fee) => acc + Number(fee.monthly_fee),
      0,
    );
    return totalDebt;
  }

  private async updatePreviousDebtsStatus(accountId: string) {
    await this.serviceFeeRepository.update(
      {
        account: { id: accountId },
        payment_status: ServiceFeePaymentStatusEnum.PENDING,
      },
      {
        payment_status: ServiceFeePaymentStatusEnum.PAID,
        last_payment_date: new Date(),
      },
    );
  }

  private async createMonthlyBillingRecord(
    account: AccountEntity,
  ): Promise<boolean> {
    const previousBills = await this.serviceFeeRepository.find({
      where: {
        account: { id: account.id },
        payment_status: ServiceFeePaymentStatusEnum.PENDING,
      },
    });
    let debtAmount = 99.9;
    let fee_accumulation_period = 0;

    if (previousBills.length > 0) {
      previousBills.forEach((bill) => {
        debtAmount += Number(bill.debt_amount);
        fee_accumulation_period += 1;
      });
    }

    const newServiceFee = new ServiceFeeEntity();
    newServiceFee.account = account;
    newServiceFee.monthly_fee = 99.9;
    newServiceFee.debt_amount = Number(debtAmount) || 99.9;
    newServiceFee.payment_status = ServiceFeePaymentStatusEnum.PENDING;
    newServiceFee.fee_accumulation_period = fee_accumulation_period;

    await this.serviceFeeRepository.save(newServiceFee);

    const paymentResult = await this.attemptPayment(account, newServiceFee);
    return paymentResult;
  }

  private async attemptPayment(
    account: AccountEntity,
    serviceFee: ServiceFeeEntity,
  ): Promise<boolean> {
    const updatedServiceFee = { ...serviceFee };

    const totalDebt = updatedServiceFee.debt_amount;
    let balanceResponse: IGetBalanceApiResponse;

    if (account.owner) {
      balanceResponse = await this.balanceService.getBalance({
        DocumentNumber: account.owner.cpf,
        Account: account.number,
      });
    }
    if (account.business) {
      balanceResponse = await this.balanceService.getBalance({
        DocumentNumber: account.business.cnpj,
        Account: account.number,
      });
    }

    if (serviceFee.debt_amount <= balanceResponse.body.amount) {
      try {
        const uuid = v4();
        const api = await this.celcoinService.transactionPixKey({
          amount: Number(totalDebt),
          initiationType: 'MANUAL',
          paymentType: 'IMMEDIATE',
          remittanceInformation: 'Taxa adminsitrativa',
          transactionType: 'TRANSFER',
          urgency: 'HIGH',
          clientCode: uuid,
          creditParty: {
            bank: process.env.SERVICE_FEE_BANK,
            account: process.env.SERVICE_FEE_ACCOUNT_NUMBER,
            branch: '0001',
            taxId: process.env.SERVICE_FEE_DOCUMENT,
            name: process.env.SERVICE_FEE_ACCOUNT_NAME,
            accountType: 'TRAN',
          },
          debitParty: {
            account: account.number,
          },
        });
        logger.info('ServiceFeeService.attemptPayment() -> ', api);

        updatedServiceFee.payment_status = ServiceFeePaymentStatusEnum.PAID;
        updatedServiceFee.last_payment_date = new Date();
        updatedServiceFee.external_id = api?.body?.id;

        await this.serviceFeeRepository.save(updatedServiceFee);
        await this.updatePreviousDebtsStatus(account.id);

        return true;
      } catch (error) {
        logger.error('ServiceFeeService.attemptPayment() -> ', error);

        updatedServiceFee.payment_status = ServiceFeePaymentStatusEnum.PENDING;
        await this.serviceFeeRepository.save(updatedServiceFee);
        return false;
      }
    } else {
      return false;
    }
  }
}
