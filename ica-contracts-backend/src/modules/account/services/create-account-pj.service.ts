import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as bcrypt from 'bcrypt';
import { addHours, format } from 'date-fns';
import { AccountCelcoinService } from 'src/apis/celcoin/services/account-celcoin.service';
import { AccountTransferLimitEntity } from 'src/shared/database/typeorm/entities/account-transfer-limits.entity';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { AddressEntity } from 'src/shared/database/typeorm/entities/address.entity';
import { BusinessEntity } from 'src/shared/database/typeorm/entities/business.entity';
import { OwnerBusinessRelationEntity } from 'src/shared/database/typeorm/entities/owner-business-relation.entity';
import { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';
import { OwnerEntity } from 'src/shared/database/typeorm/entities/owner.entity';
import { RoleEntity } from 'src/shared/database/typeorm/entities/role.entity';
import { AccountStatusEnum } from 'src/shared/enums/account-status.enum';
import { generatePassword } from 'src/shared/functions/generate-password';
import { logger } from 'src/shared/logger';
import { Equal, Repository } from 'typeorm';
import { v4 } from 'uuid';

import { CreateAccountPjDto } from '../dto/create-account-pj.dto';

@Injectable()
export class CreateAccountPJService {
  constructor(
    @InjectRepository(OwnerEntity)
    private ownerRepository: Repository<OwnerEntity>,
    @InjectRepository(AccountEntity)
    private accountDb: Repository<AccountEntity>,
    @InjectRepository(AddressEntity)
    private addressRepository: Repository<AddressEntity>,
    @InjectRepository(BusinessEntity)
    private businessRepository: Repository<BusinessEntity>,
    @InjectRepository(OwnerBusinessRelationEntity)
    private ownerBusinessRepository: Repository<OwnerBusinessRelationEntity>,
    @InjectRepository(AccountTransferLimitEntity)
    private accountLimitRepo: Repository<AccountTransferLimitEntity>,
    @InjectRepository(OwnerRoleRelationEntity)
    private ownerRoleDb: Repository<OwnerRoleRelationEntity>,
    @InjectRepository(RoleEntity)
    private roleDb: Repository<RoleEntity>,
    @Inject(AccountCelcoinService)
    private apiCelcoin: AccountCelcoinService,
  ) {}

  async perform(data: CreateAccountPjDto) {
    const owner = await this.ownerRepository.findOne({
      relations: {
        address: true,
      },
      where: {
        cpf: Equal(data.owner.cpf),
      },
    });
    const password = data.password?.trim() || generatePassword();
    const passwordEncrypted = await bcrypt.hash(password, 10);
    let ownerId: string;
    let ownerPJ;

    if (owner) {
      ownerPJ = {
        phoneNumber: owner.phone.trim(),
        documentNumber: owner.cpf.trim(),
        email: owner.email.toLowerCase(),
        fullName: owner.name.trim().toUpperCase(),
        motherName: owner.motherName.trim().toUpperCase(),
        isPoliticallyExposedPerson: Boolean(owner.pep),
        birthDate: format(owner.dtBirth, 'dd-MM-yyyy'),
        socialName: owner.nickname.trim().toUpperCase(),
        address: {
          neighborhood: owner.address[0].neighborhood,
          city: owner.address[0].city,
          postalCode: owner.address[0].cep,
          addressComplement: owner.address[0].complement,
          number: owner.address[0].number,
          state: owner.address[0].state.trim().toUpperCase(),
          street: owner.address[0].street,
        },
      };
    } else {
      ownerPJ = {
        phoneNumber: data.owner.phoneNumber.trim(),
        documentNumber: data.owner.cpf.trim(),
        email: data.owner.email.toLowerCase(),
        fullName: data.owner.fullName.trim().toUpperCase(),
        motherName: data.owner.motherName.trim().toUpperCase(),
        isPoliticallyExposedPerson: data.owner.pep,
        birthDate: format(addHours(data.owner.birthDate, 3), 'dd-MM-yyyy'),
        socialName: data.owner.socialName.trim().toUpperCase(),
        address: {
          neighborhood: data.owner.address.neighborhood,
          city: data.owner.address.city,
          postalCode: data.owner.address.cep,
          addressComplement: data.owner.address.complement,
          number: data.owner.address.number,
          state: data.owner.address.state.trim().toUpperCase(),
          street: data.owner.address.street,
        },
      };
    }

    logger.info(`CreateAccountPJService.perform() -> [ owner: ${ownerPJ}]`);

    const createCelcoin = await this.apiCelcoin.createPJ({
      clientCode: v4(),
      accountOnboardingType: 'BANKACCOUNT',
      documentNumber: data.cnpj,
      contactNumber: data.phoneNumber,
      businessEmail: data.email,
      businessName: data.companyName,
      tradingName: data.fantasyName,
      owner: [ownerPJ],
      cadastraChavePix: data.registerPixKey,
      businessAddress: {
        neighborhood: data.address.neighborhood,
        city: data.address.city,
        postalCode: data.address.cep,
        addressComplement: data.address.complement,
        number: data.address.number,
        state: data.address.state,
        street: data.address.street,
        latitude: data.address.latitude,
        longitude: data.address.longitude,
      },
    });

    logger.info(
      `CreateAccountPJService.perform() -> [ AccountOnCelcoin: ${createCelcoin}]`,
    );

    if (!owner) {
      const createOwner = this.ownerRepository.create({
        name: data.owner.fullName.trim().toUpperCase(),
        cpf: data.owner.cpf.trim(),
        email: data.owner.email.toLowerCase(),
        phone: data.owner.phoneNumber.trim(),
        motherName: data.owner.motherName.trim().toUpperCase(),
        nickname: data.owner.socialName.trim().toUpperCase(),
        dtBirth: data.owner.birthDate,
        pep: String(data.owner.pep),
      });

      const saveOwner = await this.ownerRepository.save(createOwner);

      ownerId = saveOwner.id;
      await this.addressRepository.save({
        ownerId: saveOwner.id,
        cep: data.owner.address.cep,
        city: data.owner.address.city,
        complement: data.owner.address.complement,
        neighborhood: data.owner.address.neighborhood,
        number: data.owner.address.number,
        state: data.owner.address.state.trim().toUpperCase(),
        street: data.owner.address.street,
      });
    } else {
      ownerId = owner.id;
    }

    let savedBusiness: BusinessEntity;

    savedBusiness = await this.businessRepository.findOne({
      where: { cnpj: data.cnpj },
    });

    if (!savedBusiness) {
      savedBusiness = await this.businessRepository.save({
        companyName: data.companyName,
        fantasyName: data.fantasyName,
        cnpj: data.cnpj,
        type: data.type,
        size: data.size,
        email: data.email,
        password: passwordEncrypted,
        dtOpening: data.dtOpening,
        ownerId,
      });
    }

    await this.addressRepository.save({
      businessId: savedBusiness.id,
      cep: data.address.cep,
      city: data.address.city,
      complement: data.address.complement,
      neighborhood: data.address.neighborhood,
      number: data.address.number,
      state: data.address.state,
      street: data.address.street,
    });

    let account: AccountEntity;
    account = await this.accountDb.findOne({
      where: { businessId: Equal(savedBusiness.id) },
    });

    if (!account) {
      account = await this.accountDb.save({
        businessId: savedBusiness.id,
        status: AccountStatusEnum.KYC_PENDING,
        type: 'business',
        externalId: createCelcoin.body.onBoardingId,
        isTaxable: data.isTaxable,
      });
    } else {
      await this.accountDb.update(account.id, {
        externalId: createCelcoin.body.onBoardingId,
        number: null,
        branch: null,
      });
    }

    await this.accountLimitRepo.save({ account });

    const getRole = await this.roleDb.findOne({
      where: {
        name: data.role,
      },
    });

    const createOwnerRole = this.ownerRoleDb.create({
      businessId: savedBusiness.id,
      roleId: getRole.id,
    });

    await this.ownerRoleDb.save(createOwnerRole);

    await this.ownerBusinessRepository.save({
      businessId: savedBusiness.id,
      ownerId,
      role: 'owner',
    });

    return {
      id: account.id,
    };
  }
}
