import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AccountCelcoinService } from 'src/apis/celcoin/services/account-celcoin.service';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { Equal, Repository } from 'typeorm';

import { IChangeAccountStatusRequestDto } from '../dto/change-account-status.dto';

@Injectable()
export class ChangeAccountStatusService {
  constructor(
    @InjectRepository(AccountEntity)
    private accountDb: Repository<AccountEntity>,
    @Inject(AccountCelcoinService)
    private apiCelcoin: AccountCelcoinService,
  ) {}

  async execute(data: IChangeAccountStatusRequestDto, id: string) {
    const account = await this.accountDb.findOne({
      relations: {
        owner: true,
        document: true,
        business: {
          ownerBusinessRelation: {
            owner: true,
          },
        },
      },
      where: [
        {
          ownerId: Equal(id),
        },
        {
          businessId: Equal(id),
        },
        {
          id: Equal(id),
        },
      ],
    });

    if (!account) throw new NotFoundException('Conta não encontrada');

    const document =
      account.type === 'physical' ? account.owner.cpf : account.business.cnpj;

    await this.apiCelcoin.changeAccountStatus({
      account: account.number,
      document,
      reason: data.reason,
      status: data.status,
    });
  }
}
