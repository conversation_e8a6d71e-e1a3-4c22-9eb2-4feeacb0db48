import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { AddressEntity } from 'src/shared/database/typeorm/entities/address.entity';
import { BusinessEntity } from 'src/shared/database/typeorm/entities/business.entity';
import { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';
import { OwnerEntity } from 'src/shared/database/typeorm/entities/owner.entity';
import { WalletsViewsEntity } from 'src/shared/database/typeorm/entities/wallets-views.entity';
import { Repository } from 'typeorm';

import {
  IAddressResponse,
  IBankAccountResponse,
  IBusinessInfoResponse,
  IUserResponse,
} from '../response/get-info-response';

type OwnerWithRelations = OwnerEntity & {
  address?: AddressEntity[];
  account?: AccountEntity[];
};

type BusinessWithRelations = BusinessEntity & {
  address?: AddressEntity[];
  account?: AccountEntity[];
  owner?: OwnerWithRelations;
};

type RoleRelationWithOwner = OwnerRoleRelationEntity & {
  owner: OwnerWithRelations;
  role?: { name: string };
};

@Injectable()
export class GetUserInfoService {
  private static readonly CNPJ_LENGTH = 14;

  constructor(
    @InjectRepository(OwnerRoleRelationEntity)
    private readonly ownerRoleRelationRepository: Repository<OwnerRoleRelationEntity>,
    @InjectRepository(BusinessEntity)
    private readonly businessRepository: Repository<BusinessEntity>,
    @InjectRepository(WalletsViewsEntity)
    private readonly walletsViewsRepository: Repository<WalletsViewsEntity>,
  ) {}

  async getUserInfo(
    document: string,
    roleId: string,
    fullInfo: boolean = true,
    requesterOwnerId: string,
  ): Promise<IBusinessInfoResponse | IUserResponse> {
    const isBusiness = document.length === GetUserInfoService.CNPJ_LENGTH;
    if (isBusiness) {
      return await this.getBusinessBasedUserInfo(
        document,
        roleId,
        requesterOwnerId,
      );
    }

    const result = await this.getRoleBasedUserInfo(
      document,
      roleId,
      fullInfo,
      requesterOwnerId,
    );
    return result;
  }

  private async getBusinessBasedUserInfo(
    cnpj: string,
    roleId: string,
    requesterOwnerId: string,
  ): Promise<IBusinessInfoResponse> {
    const business = await this.getBusinessWithRelations(cnpj);
    if (!business) {
      throw new NotFoundException('Empresa não encontrada');
    }

    await this.validateRequesterAccess(roleId, requesterOwnerId);
    await this.validateBusinessAccess(business, roleId);

    return this.mapBusinessToResponse(business);
  }

  /**
   * Valida se o usuário solicitante tem permissão para acessar os dados.
   * Verifica se o roleId existe e se corresponde ao requesterOwnerId.
   */
  private async validateRequesterAccess(
    roleId: string,
    requesterOwnerId: string,
  ): Promise<void> {
    const requesterRoleRelation = await this.getRequesterRoleRelation(roleId);
    if (!requesterRoleRelation) {
      throw new NotFoundException(`Papel ${roleId} não encontrado.`);
    }

    if (requesterRoleRelation.ownerId !== requesterOwnerId) {
      throw new NotFoundException(
        `Usuário ${roleId} não corresponde ao owner ${requesterOwnerId}.`,
      );
    }
  }

  /**
   * Valida se o usuário tem acesso aos dados da empresa.
   * Verifica se existe vínculo entre o solicitante e a empresa ou seus representantes.
   */
  private async validateBusinessAccess(
    business: BusinessWithRelations,
    roleId: string,
  ): Promise<void> {
    const requesterRoleRelation = await this.getRequesterRoleRelation(roleId);
    const businessRoleRelations = await this.getBusinessRoleRelations(
      business.id,
    );

    const hasAccess = await this.checkBusinessAccessPermission(
      requesterRoleRelation,
      businessRoleRelations,
    );

    if (!hasAccess) {
      throw new NotFoundException(
        'Broker não possui vínculo com esta empresa ou seus representantes',
      );
    }
  }

  private async getBusinessRoleRelations(businessId: string) {
    return await this.ownerRoleRelationRepository
      .createQueryBuilder('orr')
      .leftJoinAndSelect('orr.owner', 'owner')
      .leftJoinAndSelect('owner.address', 'address')
      .leftJoinAndSelect('orr.role', 'role')
      .where('orr.businessId = :businessId', { businessId })
      .getMany();
  }

  /**
   * Verifica se o usuário solicitante tem permissão de acesso 
   * aos dados da empresa.
   * Admin tem acesso total. Outros papéis precisam ter vínculo 
   * via WalletsViewsEntity.

   */
  private async checkBusinessAccessPermission(
    requesterRoleRelation: OwnerRoleRelationEntity & { role: { name: string } },
    businessRoleRelations: OwnerRoleRelationEntity[],
  ): Promise<boolean> {
    const requesterRole = requesterRoleRelation.role.name;

    if (requesterRole === 'admin' || requesterRole === 'superadmin') {
      return true;
    }

    for (const roleRelation of businessRoleRelations) {
      const hasAccess = await this.hasWalletViewAccess(
        requesterRoleRelation.id,
        requesterRole,
        roleRelation.id,
        roleRelation.role.name,
      );
      if (hasAccess) {
        return true;
      }
    }

    return false;
  }

  private mapBusinessToResponse(
    business: BusinessWithRelations,
  ): IBusinessInfoResponse {
    const address = this.getFirstArrayItem(
      business.address,
      (addr: AddressEntity) => this.mapAddress(addr),
    );
    const businessAccount = this.getFirstArrayItem(
      business.account,
      (acc: AccountEntity) => this.mapBankAccount(acc),
    );

    const representative = business.owner
      ? {
          type: 'owner' as const,
          id: business.owner.id,
          name: business.owner.name,
          cpf: business.owner.cpf,
          email: business.owner.email,
          phone: business.owner.phone,
          occupation: business.owner.occupation || '',
          birthDate: business.owner.dtBirth,
          nationality: business.owner.nationality || '',
          rg: business.owner.rg || '',
          rgIssuingAgency: business.owner.issuingAgency || '',
          pep: !!business.owner.pep,
          motherName: business.owner.motherName || '',
          address: this.getFirstArrayItem(
            business.owner.address,
            (addr: AddressEntity) => this.mapAddress(addr),
          ),
        }
      : undefined;

    return {
      type: 'business' as const,
      companyName: business.companyName,
      fantasyName: business.fantasyName,
      cnpj: business.cnpj,
      businessType: business.type,
      size: business.size,
      email: business.email,
      dtOpening: business.dtOpening,
      address,
      representative,
      bankAccount: businessAccount,
    };
  }

  private async getRoleBasedUserInfo(
    document: string,
    roleId: string,
    fullInfo: boolean,
    requesterOwnerId: string,
  ): Promise<IUserResponse> {
    const targetUserRoles = await this.getOwnerWithRelations(document);
    if (!targetUserRoles || targetUserRoles.length === 0) {
      throw new NotFoundException(
        `Usuário com documento ${document} não encontrado.`,
      );
    }

    await this.validateRequesterAccess(roleId, requesterOwnerId);

    const accessibleRoles = await this.getAccessibleRoles(
      roleId,
      targetUserRoles,
    );
    if (accessibleRoles.length === 0) {
      throw new NotFoundException(
        `Acesso negado ao usuário ${document} para todos os papéis.`,
      );
    }

    const baseInfo = this.mapOwner(accessibleRoles[0].owner);
    const address = this.getFirstArrayItem(
      accessibleRoles[0].owner.address,
      (addr: AddressEntity) => this.mapAddress(addr),
    );

    if (!fullInfo) {
      return {
        ...baseInfo,
        address,
      };
    }

    const bankAccount = this.getFirstArrayItem(
      accessibleRoles[0].owner.account,
      (acc: AccountEntity) => this.mapBankAccount(acc),
    );

    return {
      ...baseInfo,
      address,
      bankAccount,
    };
  }

  private async getAccessibleRoles(
    roleId: string,
    targetUserRoles: RoleRelationWithOwner[],
  ): Promise<RoleRelationWithOwner[]> {
    const requesterRoleRelation = await this.getRequesterRoleRelation(roleId);
    const accessibleRoles: RoleRelationWithOwner[] = [];

    for (const targetUser of targetUserRoles) {
      const hasAccess = await this.checkAccessPermission(
        requesterRoleRelation,
        targetUser,
      );
      if (hasAccess) {
        accessibleRoles.push(targetUser);
      }
    }

    return accessibleRoles;
  }

  private async getRequesterRoleRelation(roleId: string) {
    return await this.ownerRoleRelationRepository
      .createQueryBuilder('orr')
      .leftJoinAndSelect('orr.role', 'role')
      .where('orr.id = :roleId', { roleId })
      .getOne();
  }

  private async checkAccessPermission(
    requesterRoleRelation: OwnerRoleRelationEntity & { role: { name: string } },
    targetUser: RoleRelationWithOwner,
  ): Promise<boolean> {
    const requesterRole = requesterRoleRelation.role.name;

    // Superadmin tem acesso total
    if (requesterRole === 'superadmin') return true;

    if (requesterRoleRelation.ownerId === targetUser.ownerId) return true;

    const accessMap: Record<
      string,
      (
        requester: OwnerRoleRelationEntity & { role: { name: string } },
        target: RoleRelationWithOwner,
      ) => Promise<boolean>
    > = {
      broker: async (requester, target) =>
        this.checkBrokerAccess(requester, target),
      advisor: async (requester, target) =>
        this.checkAdvisorAccess(requester, target),
    };

    const accessChecker = accessMap[requesterRole];
    return accessChecker
      ? await accessChecker(requesterRoleRelation, targetUser)
      : false;
  }

  /**
   * Verifica se o broker tem acesso ao usuário alvo.
   * Broker pode acessar advisor e investor se estiver vinculado.
   */
  private async checkBrokerAccess(
    requesterRoleRelation: OwnerRoleRelationEntity & { role: { name: string } },
    targetUser: RoleRelationWithOwner,
  ): Promise<boolean> {
    const targetRole = targetUser.role?.name;
    const allowedRoles = ['advisor', 'investor'];

    if (!targetRole || !allowedRoles.includes(targetRole)) {
      return false;
    }

    return this.hasWalletViewAccess(
      requesterRoleRelation.id,
      'broker',
      targetUser.id,
      targetRole,
    );
  }

  /**
   * Verifica se o advisor tem acesso ao usuário alvo.
   * Advisor pode acessar broker e investor se estiver vinculado.
   */
  private async checkAdvisorAccess(
    requesterRoleRelation: OwnerRoleRelationEntity & { role: { name: string } },
    targetUser: RoleRelationWithOwner,
  ): Promise<boolean> {
    const targetRole = targetUser.role?.name;
    const allowedRoles = ['broker', 'investor'];

    if (!targetRole || !allowedRoles.includes(targetRole)) {
      return false;
    }

    return this.hasWalletViewAccess(
      requesterRoleRelation.id,
      'advisor',
      targetUser.id,
      targetRole,
    );
  }

  private async getOwnerWithRelations(
    document: string,
  ): Promise<RoleRelationWithOwner[]> {
    return await this.ownerRoleRelationRepository
      .createQueryBuilder('orr')
      .leftJoinAndSelect('orr.owner', 'owner')
      .leftJoinAndSelect('orr.role', 'role')
      .leftJoinAndSelect('owner.address', 'address')
      .leftJoinAndSelect('owner.account', 'account')
      .where('owner.cpf = :document', { document })
      .getMany();
  }

  /**
   * Verifica se existe vínculo de acesso entre dois papéis
   * via WalletsViewsEntity.
   */
  private async hasWalletViewAccess(
    upperId: string,
    upperRole: string,
    bottomId: string,
    bottomRole: string,
  ): Promise<boolean> {
    const walletView = await this.walletsViewsRepository
      .createQueryBuilder('wv')
      .leftJoinAndSelect('wv.upper', 'upper')
      .leftJoinAndSelect('upper.role', 'upperRole')
      .leftJoinAndSelect('wv.bottom', 'bottom')
      .leftJoinAndSelect('bottom.role', 'bottomRole')
      .where('wv.upperId = :upperId', { upperId })
      .andWhere('upperRole.name = :upperRole', { upperRole })
      .andWhere('wv.bottomId = :bottomId', { bottomId })
      .andWhere('bottomRole.name = :bottomRole', { bottomRole })
      .getOne();

    return !!walletView;
  }

  private async getBusinessWithRelations(
    cnpj: string,
  ): Promise<BusinessWithRelations | null> {
    return await this.businessRepository
      .createQueryBuilder('business')
      .leftJoinAndSelect('business.address', 'address')
      .leftJoinAndSelect('business.owner', 'owner')
      .leftJoinAndSelect('owner.address', 'ownerAddress')
      .leftJoinAndSelect('business.account', 'account')
      .where('business.cnpj = :cnpj', { cnpj })
      .getOne();
  }

  /**
   * Método utilitário para obter com segurança o primeiro
   * item de um array e aplicar uma função de transformação.
   */
  private getFirstArrayItem<T, R>(
    array: T[] | undefined,
    mapper: (item: T) => R,
  ): R | undefined {
    return Array.isArray(array) && array.length > 0
      ? mapper(array[0])
      : undefined;
  }

  private mapAddress(address: AddressEntity): IAddressResponse {
    return {
      id: address.id,
      cep: address.cep,
      street: address.street,
      neighborhood: address.neighborhood,
      number: address.number,
      city: address.city,
      state: address.state,
      complement: address.complement,
    };
  }

  private mapBankAccount(account: AccountEntity): IBankAccountResponse {
    return {
      id: account.id,
      bank: account.bank,
      number: account.number,
      branch: account.branch,
      type: account.type,
      status: account.status,
    };
  }

  private mapOwner(owner: OwnerEntity): IUserResponse {
    return {
      type: 'owner',
      id: owner.id,
      name: owner.name,
      cpf: owner.cpf,
      email: owner.email,
      phone: owner.phone,
      occupation: owner.occupation,
      birthDate: owner.dtBirth,
      nationality: owner.nationality,
      rg: owner.rg,
      rgIssuingAgency: owner.issuingAgency,
      pep: !!owner.pep,
      motherName: owner.motherName,
    };
  }
}
