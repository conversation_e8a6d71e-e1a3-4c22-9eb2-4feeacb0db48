import { Inject, Injectable } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { DocumentInfoService } from 'src/apis/assinatura-rbm/services/list-document.service';
import { CreateNotificationService } from 'src/modules/notifications/services/create-notification.service';
import { CreateAccountWalletService } from 'src/modules/wallets/services/create-account-wallet.service';
import { VinculateAccountRoleWalletService } from 'src/modules/wallets/services/vinculate-account-role-wallet.service';
import { VinculateInvestorAdvisorWalletService } from 'src/modules/wallets/services/vinculate-investor-advisor-wallet.service';
import { VinculateInvestorBrokerWalletService } from 'src/modules/wallets/services/vinculate-investor-broker-wallet.service';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { ContractEventEntity } from 'src/shared/database/typeorm/entities/contract-event.entity';
import { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';
import { NotificationTypeEnum } from 'src/shared/database/typeorm/entities/notification.entity';
import { OwnerRoleRelationEntity } from 'src/shared/database/typeorm/entities/owner-role-relation.entity';
import { PreRegisterEntity } from 'src/shared/database/typeorm/entities/pre-register.entity';
import { AccountTypeEnum } from 'src/shared/enums/account-type.enum';
import { ContractStatusEnum } from 'src/shared/enums/contract-status.enum';
import { RolesEnum } from 'src/shared/enums/roles.enum';
import { Equal, Repository } from 'typeorm';

@Injectable()
export class CronCreateAccountService {
  constructor(
    @InjectRepository(AccountEntity)
    private accountDb: Repository<AccountEntity>,
    @InjectRepository(PreRegisterEntity)
    private preRegister: Repository<PreRegisterEntity>,
    @InjectRepository(ContractEntity)
    private contractDb: Repository<ContractEntity>,
    @InjectRepository(OwnerRoleRelationEntity)
    private profileDb: Repository<OwnerRoleRelationEntity>,
    @Inject(DocumentInfoService)
    private assinaturaApi: DocumentInfoService,
    @Inject(CreateAccountWalletService)
    private createAccountWallet: CreateAccountWalletService,
    @Inject(VinculateAccountRoleWalletService)
    private vincRole: VinculateAccountRoleWalletService,
    @Inject(VinculateInvestorBrokerWalletService)
    private vincForBroker: VinculateInvestorBrokerWalletService,
    @Inject(VinculateInvestorAdvisorWalletService)
    private vincForAdvisor: VinculateInvestorAdvisorWalletService,

    @InjectRepository(ContractEventEntity)
    private contractEventRepository: Repository<ContractEventEntity>,
  ) {}

  // @Cron('*/10 * * * *')
  async perform() {
    const preRegisters = await this.preRegister.find({
      relations: {
        contract: {
          ownerRoleRelation: {
            role: true,
          },
          contractAdvisors: true,
        },
      },
      where: {
        contract: [
          { status: ContractStatusEnum.DRAFT },
          { status: ContractStatusEnum.AWAITING_INVESTOR_SIGNATURE },
        ],
      },
    });

    for (const preRegister of preRegisters) {
      try {
        if (preRegister.contract.externalId) {
          const contractApi = await this.assinaturaApi.getDocumentInfo(
            preRegister.contract.externalId,
          );

          if (contractApi && contractApi.documento?.status === 'finalizado') {
            const account = await this.accountDb.findOne({
              relations: {
                business: { ownerRoleRelation: true },
                owner: { ownerRoleRelation: true },
              },
              where: [
                { owner: { cpf: Equal(preRegister.document) } },
                { business: { cnpj: Equal(preRegister.document) } },
              ],
            });

            if (account) {
              const investor = await this.profileDb.findOne({
                where: {
                  ownerId: account.ownerId,
                  businessId: account.businessId,
                  role: { name: 'investor' },
                },
              });

              await this.contractDb.update(preRegister.contract.id, {
                investor,
              });

              await this.vincForBroker.perform({
                brokerId: preRegister.contract.brokerId,
                investorId: investor.id,
              });

              const createAdvisorPromises =
                preRegister.contract.contractAdvisors.map((advisor) =>
                  this.vincForAdvisor.perform({
                    investorId: investor.id,
                    advisorId: advisor.advisorId,
                  }),
                );

              await Promise.all([createAdvisorPromises]);
            } else {
              const account = await this.createAccountWallet.perform(
                {
                  accountType: AccountTypeEnum.NATURAL_PERSON,
                  owner: {
                    birthDate: preRegister.dtBirth,
                    cpf: preRegister.document,
                    email: preRegister.email,
                    fullName: preRegister.name,
                    isTaxable: true,
                    motherName: preRegister.motherName || '',
                    pep: false,
                    phoneNumber: preRegister.phoneNumber,
                    socialName: preRegister.name,
                    address: {
                      cep: preRegister.zipCode,
                      city: preRegister.city,
                      neighborhood: preRegister.neighborhood,
                      number: preRegister.addressNumber,
                      state: preRegister.state || '',
                      street: preRegister.addressComplement,
                      complement: preRegister.addressComplement,
                    },
                  },
                },
                RolesEnum.INVESTOR,
              );

              const vinc = await this.vincRole.perform({
                accountId: account.account,
                role: RolesEnum.INVESTOR,
              });

              await this.contractDb.update(preRegister.contract.id, {
                investor: { id: vinc.id },
                status: ContractStatusEnum.ACTIVE,
              });

              const broker = await this.profileDb.findOne({
                where: {
                  ownerId: preRegister.contract.ownerRoleRelation.ownerId,
                  role: { name: 'broker' },
                },
                relations: { bottom: true },
              });

              if (broker && broker.bottom.length > 0) {
                const adminId = broker.bottom[0].upperId;

                await this.contractEventRepository.save({
                  contract: { id: preRegister.contract.id },
                  eventType: 'APROVADO',
                  eventDate: new Date(),
                  responsibleId: adminId,
                });
              }

              await this.vincForBroker.perform({
                brokerId: preRegister.contract.ownerRoleRelation.id,
                investorId: vinc.id,
              });

              const createAdvisorPromises =
                preRegister.contract.contractAdvisors.map((advisor) =>
                  this.vincForAdvisor.perform({
                    investorId: vinc.id,
                    advisorId: advisor.advisorId,
                  }),
                );

              await Promise.all([createAdvisorPromises]);
            }
          }
        }
      } catch (error) {
        console.error(
          `Erro ao processar preRegister com contractId: ${preRegister.contract.id}`,
          error,
        );
      }
    }
  }
}
