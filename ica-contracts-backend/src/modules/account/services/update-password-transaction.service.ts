import { BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as bcrypt from 'bcrypt';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { Equal, Repository } from 'typeorm';

import { UpdatePasswordTransactionDto } from '../dto/update-password-transaction.dto';

export class UpdatePasswordTransactionService {
  constructor(
    @InjectRepository(AccountEntity)
    private accountRepository: Repository<AccountEntity>,
  ) {}

  async perform(id: string, data: UpdatePasswordTransactionDto): Promise<void> {
    const account = await this.accountRepository.findOne({
      where: [
        {
          ownerId: Equal(id),
        },
        {
          businessId: Equal(id),
        },
        {
          id: Equal(id),
        },
      ],
    });

    if (!account) {
      throw new NotFoundException('Conta não encontrada.');
    }

    const oldPasswordMatches = await bcrypt.compare(
      data.oldPassword,
      account.transactionPassword,
    );

    if (!oldPasswordMatches) {
      throw new BadRequestException(
        'A senha de transação antiga está incorreta.',
      );
    }

    const hashedNewPassword = await bcrypt.hash(data.newPassword, 10);

    account.transactionPassword = hashedNewPassword;

    await this.accountRepository.save(account);
  }
}
