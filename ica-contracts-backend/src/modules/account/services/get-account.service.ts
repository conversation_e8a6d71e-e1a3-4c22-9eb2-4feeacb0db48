import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AccountCelcoinService } from 'src/apis/celcoin/services/account-celcoin.service';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { StatisticEntity } from 'src/shared/database/typeorm/entities/statistic.entity';
import { AccountStatusEnum } from 'src/shared/enums/account-status.enum';
import { Equal, Repository } from 'typeorm';

import { IGetAccountResponse } from '../response/get-account-response';

@Injectable()
export class GetAccountService {
  constructor(
    @Inject(AccountCelcoinService)
    private apiCelcoin: AccountCelcoinService,
    @InjectRepository(AccountEntity)
    private accountDb: Repository<AccountEntity>,
    @InjectRepository(StatisticEntity)
    private statisticDb: Repository<StatisticEntity>,
  ) {}

  async perform(id: string): Promise<IGetAccountResponse> {
    const account = await this.accountDb.findOne({
      relations: {
        owner: {
          ownerRoleRelation: {
            role: true,
          },
        },
        document: true,
        business: {
          ownerBusinessRelation: {
            owner: true,
          },
          ownerRoleRelation: {
            role: true,
          },
        },
      },
      where: [
        {
          ownerId: Equal(id),
        },
        {
          businessId: Equal(id),
        },
        {
          id: Equal(id),
        },
      ],
    });

    if (!account) throw new NotFoundException('Conta não encontrada.');

    const statistic = await this.statisticDb.find({
      order: {
        createdAt: 'DESC',
      },
      take: 1,
    });

    const transactionPasswordExists = !!account.transactionPassword;
    const roles: Array<{ name: string; roleId: string }> = [];
    if (account.type === 'physical') {
      account.owner.ownerRoleRelation.map((roleRelation) => {
        roles.push({
          name: roleRelation.role.name,
          roleId: roleRelation.id,
        });
      });
    } else {
      account.business.ownerRoleRelation.map((roleRelation) => {
        roles.push({
          name: roleRelation.role.name,
          roleId: roleRelation.id,
        });
      });
    }

    const api = await this.apiCelcoin
      .getAccount(
        {
          Account: account.number,
          DocumentNumber: account.owner?.cpf || account.business.cnpj,
        },
        account.type,
      )
      .catch(() => {
        return undefined;
      });

    return {
      id: account.id,
      profileImage: account.profileImage,
      externalId: account.externalId,
      temporaryPassword:
        account.type === 'physical'
          ? account.owner.temporaryPassword
          : account.business.temporaryPassword,
      owner:
        account.type === 'physical'
          ? {
              cpf: account.owner.cpf,
              name: account.owner.name,
              motherName: account.owner.motherName,
              email: account.owner.email,
              dtBirth: account.owner.dtBirth,
              nickname: account.owner.nickname,
              phone: account.owner.phone,
              pep: account.owner.pep,
              createdAt: account.owner.createdAt,
              updatedAt: account.owner.updatedAt,
              deletedAt: account.owner.deletedAt,
            }
          : undefined,
      status: account.status as AccountStatusEnum,
      type: account.type,
      business:
        account.type === 'business'
          ? {
              cnpj: account.business.cnpj,
              companyName: account.business.companyName,
              fantasyName: account.business.fantasyName,
              email: account.business.email,
              dtOpening: account.business.dtOpening,
              size: account.business.size,
              type: account.business.type,
              createdAt: account.business.createdAt,
              updatedAt: account.business.updatedAt,
              deletedAt: account.business.deletedAt,
            }
          : undefined,
      account: api
        ? {
            branch:
              account.type === 'physical'
                ? api.body.account.branch
                : api.body.businessAccount.branch,
            number:
              account.type === 'physical'
                ? api.body.account.account
                : api.body.businessAccount.account,
            transactionPasswordExists,
          }
        : undefined,
      prefeitura:
        account.type === 'business'
          ? {
              startContract: account.business.startContract,
              endContract: account.business.endContract,
              totalInvested: Number(account.business.totalInvested),
              yield: Number(account.business.yield),
              profitability:
                account.business.totalInvested * account.business.yield || 0,
              investors: statistic[0].investors,
              totalApplied: statistic[0].totalApplied,
              statesData: statistic[0].statesData,
            }
          : {
              startContract: account.owner.startContract,
              endContract: account.owner.endContract,
              totalInvested: Number(account.owner.totalInvested),
              yield: Number(account.owner.yield),
              profitability:
                account.owner.totalInvested * account.owner.yield || 0,
              investors: statistic[0].investors,
              totalApplied: statistic[0].totalApplied,
              statesData: statistic[0].statesData,
            },
      roles,
    };
  }
}
