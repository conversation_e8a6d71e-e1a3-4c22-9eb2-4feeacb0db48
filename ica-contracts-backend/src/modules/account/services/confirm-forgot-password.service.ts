import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as bcrypt from 'bcrypt';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { BusinessEntity } from 'src/shared/database/typeorm/entities/business.entity';
import { OwnerEntity } from 'src/shared/database/typeorm/entities/owner.entity';
import { EmailService } from 'src/shared/email/email.service';
import { generatePassword } from 'src/shared/functions/generate-password';
import { Equal, Repository } from 'typeorm';

import { ConfirmForgotPasswordDto } from '../dto/confirm-forgot-password.dto';

@Injectable()
export class ConfirmForgotPasswordService {
  constructor(
    @InjectRepository(AccountEntity)
    private accountDb: Repository<AccountEntity>,
    @InjectRepository(OwnerEntity)
    private ownerDb: Repository<OwnerEntity>,
    @InjectRepository(BusinessEntity)
    private businessDb: Repository<BusinessEntity>,
    private readonly emailService: EmailService,
  ) {}

  async perform(data: ConfirmForgotPasswordDto) {
    const account = await this.accountDb.findOne({
      relations: {
        owner: true,
        document: true,
        business: {
          ownerBusinessRelation: {
            owner: true,
          },
        },
      },
      where: [
        {
          business: {
            cnpj: Equal(data.document),
          },
        },
        {
          owner: {
            cpf: Equal(data.document),
          },
        },
      ],
    });

    if (!account) throw new NotFoundException('Conta não encontrada.');

    const phone =
      account.type === 'physical'
        ? account.owner.phone
        : account.business.ownerBusinessRelation[0].owner.phone;

    const email =
      account.type === 'physical'
        ? account.owner.email
        : account.business.email;

    if (phone !== data.phone || email !== data.email) {
      throw new BadRequestException('Informações não correspondem');
    }

    const password = generatePassword();
    const passwordEncrypted = await bcrypt.hash(password, 10);

    this.emailService
      .sendEmail({
        to: account.owner?.email || account.business.email,
        subject: 'recuperação de senha',
        template: 'welcome',
        context: {
          document: account.owner?.cpf || account.business.cnpj,
          password,
          username: account.owner?.name || account.business.companyName,
        },
      })
      .then(() => console.log('Enviado e-mail com sucesso'))
      .catch((err) => console.log('Erro ao enviar email', err));

    if (account.type === 'physical') {
      await this.ownerDb.update(account.ownerId, {
        password: passwordEncrypted,
        temporaryPassword: true,
      });
    } else {
      await this.businessDb.update(account.businessId, {
        password: passwordEncrypted,
        temporaryPassword: true,
      });
    }
  }
}
