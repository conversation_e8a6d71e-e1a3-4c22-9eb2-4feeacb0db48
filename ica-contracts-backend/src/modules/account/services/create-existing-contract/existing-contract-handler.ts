import { AccountEntity } from '../../../../shared/database/typeorm/entities/account.entity';
import { AddressEntity } from '../../../../shared/database/typeorm/entities/address.entity';
import { BusinessEntity } from '../../../../shared/database/typeorm/entities/business.entity';
import { ContractEntity } from '../../../../shared/database/typeorm/entities/contract.entity';
import { OwnerRoleRelationEntity } from '../../../../shared/database/typeorm/entities/owner-role-relation.entity';
import { OwnerEntity } from '../../../../shared/database/typeorm/entities/owner.entity';
import { PreRegisterEntity } from '../../../../shared/database/typeorm/entities/pre-register.entity';
import { RoleEntity } from '../../../../shared/database/typeorm/entities/role.entity';
import { CreateExistingContractDto, AdvisorAssignmentDto } from '../../dto/create-existing-contract.dto';

export class CreateExistingContractContext {
  
  contract?: {contractId: string};
  preRegister?: PreRegisterEntity;
  broker?: OwnerRoleRelationEntity;
  admin?: OwnerRoleRelationEntity;
  advisors: AdvisorAssignmentDto[]; 
  roleInvestor?: RoleEntity;
  ownerRoleRelation?: OwnerRoleRelationEntity;
  businessRoleRelation?: OwnerRoleRelationEntity;
  brokerId: string;

  constructor(
    public dto: CreateExistingContractDto,
    brokerId: string,
  ) {
    this.advisors = [];
    this.brokerId = brokerId
  }
}
