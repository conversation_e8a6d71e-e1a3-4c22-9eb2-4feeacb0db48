import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { WalletsViewsEntity } from 'src/shared/database/typeorm/entities/wallets-views.entity';
import { RolesEnum } from 'src/shared/enums/roles.enum';
import { In, Repository } from 'typeorm';

import { AbstractAccountHandler } from '../abstract-account-handler';
import { CreateExistingContractContext } from '../existing-contract-handler';
import { AdvisorAssignmentDto  } from 'src/modules/account/dto/create-existing-contract.dto';

@Injectable()
export class ValidateAdvisorHandler extends AbstractAccountHandler<
  CreateExistingContractContext,
  CreateExistingContractContext
> {
  constructor(
    @InjectRepository(WalletsViewsEntity)
    private readonly walletsViewsRepository: Repository<WalletsViewsEntity>,
  ) {
    super();
  }

  async handle(context: CreateExistingContractContext): Promise<CreateExistingContractContext> {
    if (context.dto.advisors?.length > 0) {
      this.logger.debug('Validando advisors enviados', context.dto.advisors);
      const isAdvisorValid = await this.areAllAdvisorsValid(
        context.dto.brokerId,
        context.dto.advisors,
      );

      if (!isAdvisorValid) {
        this.logger.error('Lista de advisors inválida', context.dto.advisors);

        throw new BadRequestException('Lista de assessores inválida');
      }

      context.dto.advisors.forEach((advisor) => {        
        context.advisors.push(advisor);
      });
      this.logger.log('Advisors adicionados ao contexto', context.advisors);
    }

    return super.handle(context);
  }

  private async areAllAdvisorsValid(
    brokerId: string,
    advisors: AdvisorAssignmentDto[],
  ): Promise<boolean> {
    const advisorsCount = advisors.length;
    const advisorsIds = advisors.map((advisor) => advisor.advisorId);

    if (advisorsCount === 0) {
      return true;
    }

    const validAdvisorsCount = await this.walletsViewsRepository.count({
      where: {
        upperId: brokerId,
        bottom: {
          id: In(advisorsIds),
          role: { name: RolesEnum.ADVISOR },
        },
      },
    });

    return validAdvisorsCount === advisorsCount;
  }
}
