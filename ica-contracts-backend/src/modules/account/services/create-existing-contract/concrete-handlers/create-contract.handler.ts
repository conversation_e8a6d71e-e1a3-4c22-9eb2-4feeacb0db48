import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { AbstractAccountHandler } from '../abstract-account-handler';
import { CreateExistingContractContext } from '../existing-contract-handler';
import { CreateExistingContractApiService } from 'src/apis/ica-contract-service/services/create-existing-contract.service';

@Injectable()
export class ContractHandler extends AbstractAccountHandler<
  CreateExistingContractContext,
  CreateExistingContractContext
> {
  constructor(
    private readonly createExistingContractService: CreateExistingContractApiService,
  ) {
    super();
  }

  public async handle(
    context: CreateExistingContractContext,
  ): Promise<CreateExistingContractContext> {
    try {
      const data = await this.createExistingContractService.perform({
        ...context.dto,
        advisors: context.advisors,
        brokerId: context.brokerId,
        personalDocument: {
          mimetype: context.dto.personalDocument.file.mimetype,
          buffer: context.dto.personalDocument.file.buffer,
        },
        proofOfPayment: {
          mimetype: context.dto.proofOfPayment.file.mimetype,
          buffer: context.dto.proofOfPayment.file.buffer,
        },
        contract: {
          mimetype: context.dto.contract.file.mimetype,
          buffer: context.dto.contract.file.buffer,
        },
        proofOfResidence: {
          mimetype: context.dto.proofOfResidence.file.mimetype,
          buffer: context.dto.proofOfResidence.file.buffer,
        },
        companyDocument: context.dto.companyDocument?.file && {
          mimetype: context.dto.companyDocument.file.mimetype,
          buffer: context.dto.companyDocument.file.buffer,
        },
      });

      context.contract = { contractId: data.contractId };

      return super.handle(context);
    } catch (error) {
      console.log(error);

      this.logger.error('Erro ao criar o contrato', error);
      throw error;
    }
  }
}
