// handlers/check-existing-contract.handler.ts
import { BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';
import { Equal, Repository } from 'typeorm';

import { AbstractAccountHandler } from '../abstract-account-handler';
import { CreateExistingContractContext } from '../existing-contract-handler';
import { CreateNotificationService } from 'src/modules/notifications/services/create-notification.service';
import { NotificationTypeEnum } from 'src/shared/database/typeorm/entities/notification.entity';
import { ContractStatusEnum } from 'src/shared/enums/contract-status.enum';

export class CheckExistingContractHandler extends AbstractAccountHandler<
CreateExistingContractContext,
CreateExistingContractContext
> {
  constructor(
    @InjectRepository(ContractEntity)
    private readonly contractRepository: Repository<ContractEntity>,
    private readonly createNotificationService: CreateNotificationService,

  ) {
    super();
  }

  public async handle(
    context: CreateExistingContractContext,
  ): Promise<CreateExistingContractContext> {
    const { dto } = context;

    try {
      const existContract = await this.contractRepository.findOne({
        where: {
          status: ContractStatusEnum.ACTIVE,
          investor: [
            {
              owner: {
                cpf: Equal(dto.individual?.cpf),
              },
            },
            {
              business: {
                cnpj: Equal(dto.company?.cnpj),
              },
            },
          ],
        },
      });

      if (existContract) {
        this.logger.error(
          `Já existe um contrato ativo para o documento: ${dto.company?.cnpj || dto.individual?.cpf}`, 
        );
        

        throw new BadRequestException(
          'Já existe um contrato ativo para este documento',
        );
      }

      this.logger.log(
        `Nenhum contrato pré-existente encontrado para o documento ${dto.company?.cnpj || dto.individual?.cpf}.`,
      );
      return super.handle(context);
    } catch (error) {
      this.logger.error(
        'Erro ao verificar contrato existente',
        error?.stack || error,
      );
      throw error;
    }
  }
}
