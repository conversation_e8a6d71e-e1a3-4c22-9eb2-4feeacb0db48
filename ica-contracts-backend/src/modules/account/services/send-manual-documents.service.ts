import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { S3 } from 'aws-sdk';
import { KYCCelcoinService } from 'src/apis/celcoin/services/kyc-celcoin.service';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { DocumentEntity } from 'src/shared/database/typeorm/entities/document.entity';
import { AccountStatusEnum } from 'src/shared/enums/account-status.enum';
import { Equal, Repository } from 'typeorm';

import { SendDocumentsAccountDto } from '../dto/send-documents-account.dto';

@Injectable()
export class SendManualDocumentsService {
  constructor(
    @InjectRepository(AccountEntity)
    private accountDb: Repository<AccountEntity>,
    @InjectRepository(DocumentEntity)
    private documentDb: Repository<DocumentEntity>,
    @Inject(KYCCelcoinService)
    private apiCelcoin: KYCCelcoinService,
  ) {}
  async perform(data: SendDocumentsAccountDto, id: string) {
    const account = await this.accountDb.findOne({
      relations: {
        owner: true,
        document: true,
        business: {
          ownerBusinessRelation: {
            owner: true,
          },
        },
      },
      where: [
        {
          ownerId: Equal(id),
        },
        {
          businessId: Equal(id),
        },
        {
          id: Equal(id),
        },
      ],
    });

    if (!account) throw new NotFoundException('Conta não encontrada.');

    if (!data.back || !data.front) {
      throw new BadRequestException('Documentos exigidos frente e verso');
    }
    if (account.type === 'business' && !data.socialContract) {
      throw new BadRequestException(
        'Contrato Social obrigatório para contas PJ',
      );
    }

    const filename = `${account.id}-${new Date()}`;
    const s3 = new S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });

    const document =
      account.type === 'physical' ? account.owner.cpf : account.business.cnpj;

    await this.apiCelcoin.sendDocuments({
      documentnumber: document,
      filetype: data.type,
      front: data.front.buffer,
      verse: data.back.buffer,
      cnpj: account.business ? account.business.cnpj : undefined,
    });
    const saveFront = await s3
      .upload({
        Bucket: process.env.S3_BUCKET,
        Key: `${filename}-FRONT.jpg`,
        Body: data.front.buffer,
        ACL: 'public-read',
      })
      .promise();

    const saveBack = await s3
      .upload({
        Bucket: process.env.S3_BUCKET,
        Key: `${filename}-BACK.jpg`,
        Body: data.back.buffer,
        ACL: 'public-read',
      })
      .promise();

    let createDoc = this.documentDb.create({
      accountId: account.id,
      back: saveBack.Key,
      front: saveFront.Key,
      sent: true,
    });

    if (account.type === 'business') {
      await this.apiCelcoin.sendDocuments({
        documentnumber: account.business.cnpj,
        filetype: 'CONTRATO_SOCIAL',
        front: data.socialContract.buffer,
        cnpj: account.business ? account.business.cnpj : undefined,
      });
      const saveContract = await s3
        .upload({
          Bucket: process.env.S3_BUCKET,
          Key: `${filename}-CONTRATO_SOCIAL.pdf`,
          Body: data.socialContract.buffer,
          ContentType: 'application/pdf',
          ACL: 'public-read',
        })
        .promise();

      createDoc = this.documentDb.create({
        ...createDoc,
        socialContract: saveContract.Key,
      });

      if (data.cardCnpj) {
        await this.apiCelcoin.sendDocuments({
          documentnumber: account.business.cnpj,
          filetype: 'CARTAO_CNPJ',
          front: data.cardCnpj.buffer,
          cnpj: account.business ? account.business.cnpj : undefined,
        });
        const saveCard = await s3
          .upload({
            Bucket: process.env.S3_BUCKET,
            Key: `${filename}-CARD_CNPJ.pdf`,
            Body: data.cardCnpj.buffer,
            ContentType: 'application/pdf',
            ACL: 'public-read',
          })
          .promise();

        createDoc = this.documentDb.create({
          ...createDoc,
          cardCNPJ: saveCard.Key,
        });
      }
    }

    await this.documentDb.save(createDoc);
    await this.accountDb.update(account.id, {
      status: AccountStatusEnum.PENDING,
    });
  }
}
