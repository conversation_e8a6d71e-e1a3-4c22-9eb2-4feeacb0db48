import { NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as AWS from 'aws-sdk';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { Repository } from 'typeorm';

export class GetDocumentsService {
  private s3: AWS.S3;

  constructor(
    @InjectRepository(AccountEntity)
    private accountDb: Repository<AccountEntity>,
  ) {
    this.s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
      region: process.env.AWS_REGION,
    });
  }

  async perform(accountId: string) {
    const account = await this.accountDb.findOne({
      relations: {
        owner: true,
        document: true,
        business: {
          ownerBusinessRelation: {
            owner: true,
          },
        },
      },
      where: [
        {
          id: accountId,
        },
      ],
    });

    if (!account) {
      throw new NotFoundException('Usuário ou documento não encontrado.');
    }

    if (!account.document || account.document.length === 0) {
      throw new NotFoundException('Nenhum documento encontrado.');
    }

    const document =
      account.document
        .filter((doc) => doc.sent === true)
        .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
        .shift() || null;

    if (!document) {
      throw new NotFoundException('Nenhum documento enviado encontrado.');
    }

    const frontUrl = this.s3.getSignedUrl('getObject', {
      Bucket: process.env.S3_BUCKET,
      Key: document.front,
    });

    const backUrl = this.s3.getSignedUrl('getObject', {
      Bucket: process.env.S3_BUCKET,
      Key: document.back,
    });

    let socialContractUrl = null;
    let cardCnpjUrl = null;

    if (account.type === 'business') {
      socialContractUrl = this.s3.getSignedUrl('getObject', {
        Bucket: process.env.S3_BUCKET,
        Key: document.socialContract,
      });
      if (document.cardCNPJ) {
        cardCnpjUrl = this.s3.getSignedUrl('getObject', {
          Bucket: process.env.S3_BUCKET,
          Key: document.cardCNPJ,
        });
      }
    }

    return {
      ...document,
      frontUrl,
      backUrl,
      cardCnpjUrl,
      socialContractUrl,
    };
  }
}
