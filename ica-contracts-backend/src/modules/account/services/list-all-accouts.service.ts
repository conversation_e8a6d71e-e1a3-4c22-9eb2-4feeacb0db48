import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AccountTransferLimitEntity } from 'src/shared/database/typeorm/entities/account-transfer-limits.entity';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { logger } from 'src/shared/logger';
import { Repository } from 'typeorm';

import { ListAllAccountsDto } from '../dto/list-all-accounts.dto';

@Injectable()
export class ListAllAccountsService {
  constructor(
    @InjectRepository(AccountEntity)
    private accountDb: Repository<AccountEntity>,
    @InjectRepository(AccountTransferLimitEntity)
    private readonly accountLimitRepo: Repository<AccountTransferLimitEntity>,
  ) {}

  async execute(input: ListAllAccountsDto): Promise<any> {
    try {
      const limit = input.limit ?? 20;
      const page = input.page ?? 0;
      const skip = page !== 0 ? (page - 1) * limit : 0;

      const [accounts, count] = await this.accountDb.findAndCount({
        where: [
          {
            status: input.status,
            owner: {
              cpf: input.document,
            },
          },
          {
            status: input.status,
            business: {
              cnpj: input.document,
            },
          },
        ],
        relations: {
          owner: true,
          business: { ownerBusinessRelation: { owner: true } },
        },
        select: {
          owner: {
            id: true,
            name: true,
            nickname: true,
            cpf: true,
            email: true,
            endContract: true,
            totalInvested: true,
            address: true,
            dtBirth: true,
          },
          business: {
            id: true,
            companyName: true,
            fantasyName: true,
            cnpj: true,
            email: true,
            endContract: true,
            totalInvested: true,
            address: true,
            ownerBusinessRelation: {
              id: true,
              owner: {
                id: true,
                name: true,
                nickname: true,
                cpf: true,
                email: true,
              },
            },
          },
        },
        take: limit,
        skip,
      });

      const accountsWithLimitRelation = await Promise.all(
        accounts.map(async (acc) => {
          const accountTransferLimit = await this.accountLimitRepo.findOne({
            where: { accountId: acc.id },
          });

          return { ...acc, accountTransferLimit };
        }),
      );

      return {
        accounts: accountsWithLimitRelation,
        count,
        itemsPerPage: accounts.length,
        page: Number(page),
        totalPages: count / limit < 1 ? 1 : Math.ceil(count / limit),
      };
    } catch (error) {
      logger.error(`ListAllAccountsService.execute() -> ${error}`);
      throw error;
    }
  }
}
