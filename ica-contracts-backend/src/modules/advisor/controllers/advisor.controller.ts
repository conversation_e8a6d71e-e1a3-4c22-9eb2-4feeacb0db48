import {
  BadRequestException,
  Body,
  Controller,
  Get,
  HttpException,
  Inject,
  Param,
  Post,
  Query,
  Request,
  UseGuards,
} from '@nestjs/common';
import { CreateOwnerRequestDto } from 'src/modules/owner/dto/create-owner-request-dto';
import { Roles } from 'src/shared/decorators/roles.decorator';
import { RolesEnum } from 'src/shared/enums/roles.enum';
import { JwtAuthGuard } from 'src/shared/guards/jwt-auth.guard';
import { RoleGuard } from 'src/shared/guards/role.guard';
import { logger } from 'src/shared/logger';

import { PeriodFilter } from 'src/modules/contract/helpers/get-contracts-growth-chart.absctraction';
import { IRequestUser } from 'src/shared/interfaces/request-user.interface';
import type { GetAdvisorContractsPaginationDto } from '../dto/get-advisor-contracts-request.dto';
import { CreateAdvisorService } from '../services/create-advisor.service';
import { AdvisorDashboardService } from '../services/dashboard.service';
import { EditAdvisorService } from '../services/edit-advisor.service';
import { GetAdvisorContractsGrowthChartService } from '../services/get-advisor-contracts-growth-chart.service';
import { GetAdvisorInfoService } from '../services/get-advisor-info.service';
import { GetAdvisorService } from '../services/get-advisor.service';
import { GetAdvisorContractsService } from '../services/get-advisors-contracts.service';
import { ContractTypeEnum } from 'src/shared/enums/contract-type.enum';
import { ListActiveInvestorsDto } from 'src/modules/income-report/dto/list-investors.dto';
import { ListActiveInvestorsAdvisorService } from '../services/list-investors.service';

@Controller('advisor')
@UseGuards(JwtAuthGuard)
export class AdvisorController {
  constructor(
    @Inject(CreateAdvisorService)
    private readonly createAdvisorService: CreateAdvisorService,
    @Inject(GetAdvisorInfoService)
    private readonly getAdvisorInfoService: GetAdvisorInfoService,
    @Inject(AdvisorDashboardService)
    private readonly dashboardService: AdvisorDashboardService,
    @Inject(EditAdvisorService)
    private readonly editAdvisorService: EditAdvisorService,
    private readonly getAdvisorService: GetAdvisorService,
    private readonly getAdvisorContractsService: GetAdvisorContractsService,
    private readonly getAdvisorContractsGrowthChartService: GetAdvisorContractsGrowthChartService,
    private readonly listActiveInvestorsAdvisorService: ListActiveInvestorsAdvisorService,
  ) {}

  // @UseGuards(RoleGuard)
  // @Roles(RolesEnum.ADMIN, RolesEnum.BROKER, RolesEnum.SUPERADMIN)
  // @Post()
  // async createAdvisor(@Body() body: CreateOwnerRequestDto) {
  //   try {
  //     return this.createAdvisorService.perform(body);
  //   } catch (error) {
  //     logger.error('AdvisorController.CreateAdvisor() -> ', error);
  //     throw new BadRequestException(error);
  //   }
  // }

  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.ADVISOR)
  @Get()
  async search(@Request() request: IRequestUser) {
    const results = await this.getAdvisorInfoService.perform({
      ownerId: request.user.id,
    });
    return results;
  }

  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.ADVISOR)
  @Get('broker')
  async getBroker(@Request() request: IRequestUser) {
    try {
      const result = await this.getAdvisorInfoService.getBrokerByAdvisorOwnerId(request.user.id);
      return result;
    } catch (error) {
      throw new HttpException(
        { error: error.response || error.message },
        error.status,
      );
    }
  }

  @Get('income-report/investors')
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.BROKER, RolesEnum.SUPERADMIN,RolesEnum.ADMIN,RolesEnum.ADVISOR)
  async findActiveInvestors(
    @Request() request: IRequestUser,
    @Query() filters: ListActiveInvestorsDto,
  ) {
    return this.listActiveInvestorsAdvisorService.perform(request.user.id, filters);
  }

  @UseGuards(RoleGuard)
  @Roles(RolesEnum.ADVISOR)
  @Get('contracts')
  async getContracts(
    @Request() request: IRequestUser,
    @Query()
    query: GetAdvisorContractsPaginationDto,
  ) {
    const results = await this.getAdvisorContractsService.perform(
      query,
      request.user.id,
    );
    return results;
  }

  @UseGuards(JwtAuthGuard, RoleGuard)
  @Get('dashboard')
  async dashboard(
    @Request() request: IRequestUser
  ) {
    const result = await this.dashboardService.perform({
      ownerId: request.user.id,
    });
    return result;
  }

  @UseGuards(JwtAuthGuard)
  @Get(':advisorId')
  async getOneAdvisor(
    @Param('advisorId') advisorId: string
  ) {
    try {
      return await this.getAdvisorService.perform(advisorId);
    } catch (error) {
      throw new HttpException(
        { error: error.response || error.message },
        error.status,
      );
    }
  }

  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.ADVISOR)
  @Get('/:ownerRoleId/contracts-growth')
  async getContractsGrowthChart(
    @Param('ownerRoleId') ownerRoleId: string,
    @Query('period') period?: PeriodFilter,
    @Query('contractType') contractType?: ContractTypeEnum,
  ) {
    return this.getAdvisorContractsGrowthChartService.perform(
      ownerRoleId,
      period,
      contractType,
    );
  }
}
