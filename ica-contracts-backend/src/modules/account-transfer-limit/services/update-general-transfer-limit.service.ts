/* eslint-disable no-param-reassign */
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AccountTransferLimitEntity } from 'src/shared/database/typeorm/entities/account-transfer-limits.entity';
import { Repository } from 'typeorm';

import { IUpdateGeneralLimitDto } from '../dto/update-general-limit.dto';

@Injectable()
export class UpdateGeneralLimitService {
  constructor(
    @InjectRepository(AccountTransferLimitEntity)
    private readonly accountLimitRepo: Repository<AccountTransferLimitEntity>,
  ) {}

  async execute(input: IUpdateGeneralLimitDto) {
    const accounts = await this.accountLimitRepo.find({
      select: { id: true },
    });

    const accountIds = accounts.map((a) => a.id);

    return this.accountLimitRepo.update(accountIds, {
      generalTransferLimit: input.generalLimit,
    });
  }
}
