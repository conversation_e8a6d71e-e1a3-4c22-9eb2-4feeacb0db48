/* eslint-disable no-param-reassign */
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AccountTransferLimitEntity } from 'src/shared/database/typeorm/entities/account-transfer-limits.entity';
import { AccountEntity } from 'src/shared/database/typeorm/entities/account.entity';
import { Equal, Repository } from 'typeorm';

@Injectable()
export class AddExtraDailyLimitService {
  constructor(
    @InjectRepository(AccountEntity)
    private readonly accountRepo: Repository<AccountEntity>,
    @InjectRepository(AccountTransferLimitEntity)
    private readonly accountLimitRepo: Repository<AccountTransferLimitEntity>,
  ) {}

  async execute(extraLimit: number, accountId: string) {
    const account = await this.accountRepo.findOne({
      where: {
        id: Equal(accountId),
      },
    });

    if (!account) throw new NotFoundException('Conta não encontrada!');

    return this.accountLimitRepo.save({
      accountId: account.id,
      dailyLimit: extraLimit,
      active: true,
    });
  }
}
