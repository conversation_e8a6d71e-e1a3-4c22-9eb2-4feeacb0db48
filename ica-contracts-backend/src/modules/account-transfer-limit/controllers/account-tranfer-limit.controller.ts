import { Body, Controller, Put, UseGuards, Request, Get } from '@nestjs/common';
import { Roles } from 'src/shared/decorators/roles.decorator';
import { RolesEnum } from 'src/shared/enums/roles.enum';
import { JwtAuthGuard } from 'src/shared/guards/jwt-auth.guard';
import { RoleGuard } from 'src/shared/guards/role.guard';
import { IRequestUser } from 'src/shared/interfaces/request-user.interface';

import { IUpdateAccountLimitDto } from '../dto/update-account-limit.dto';
import { IUpdateGeneralLimitDto } from '../dto/update-general-limit.dto';
import { IUpdateManyAccountLimitDto } from '../dto/update-many.dto';
import { AddExtraDailyLimitService } from '../services/add-extra-daily-limit.service';
import { GetAccountLimitService } from '../services/get-account-limit.service';
import { UpdateAccountLimitService } from '../services/update-account-limit.service';
import { UpdateGeneralLimitService } from '../services/update-general-transfer-limit.service';
import { UpdateManyLimitService } from '../services/update-many-limits.service';

@Controller('account-transfer-limit')
export class AccountTransferLimitController {
  constructor(
    private readonly updateAccountLimitService: UpdateAccountLimitService,
    private readonly getAccountLimitService: GetAccountLimitService,
    private readonly updateGeneralLimitService: UpdateGeneralLimitService,
    private readonly updateManyLimitService: UpdateManyLimitService,
    private readonly addExtraDailyLimitService: AddExtraDailyLimitService,
  ) {}

  @Put()
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.BROKER, RolesEnum.ADVISOR, RolesEnum.INVESTOR)
  async updateAccountLimit(
    @Request()
    request: IRequestUser,
    @Body() input: IUpdateAccountLimitDto,
  ): Promise<void> {
    await this.updateAccountLimitService.execute(input, request.user.id);
  }

  @Get()
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.BROKER, RolesEnum.ADVISOR, RolesEnum.INVESTOR)
  async getAccountLimit(
    @Request()
    request: IRequestUser,
  ) {
    return this.getAccountLimitService.execute(request.user.id);
  }

  @Put('general-limit')
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.ADMIN, RolesEnum.SUPERADMIN)
  async updateGeneralLimit(
    @Request()
    request: IRequestUser,
    @Body() input: IUpdateGeneralLimitDto,
  ): Promise<void> {
    await this.updateGeneralLimitService.execute(input);
  }

  @Put('many-limit')
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.ADMIN, RolesEnum.SUPERADMIN)
  async updateManyLimit(
    @Request()
    request: IRequestUser,
    @Body() input: IUpdateManyAccountLimitDto[],
  ): Promise<void> {
    await this.updateManyLimitService.execute(input);
  }

  @Put('add-limit')
  @UseGuards(JwtAuthGuard, RoleGuard)
  @Roles(RolesEnum.ADMIN, RolesEnum.SUPERADMIN)
  async addLimit(
    @Request()
    request: IRequestUser,
    @Body() input: { extraLimit: number; accountId: string },
  ): Promise<any> {
    return this.addExtraDailyLimitService.execute(
      input.extraLimit,
      input.accountId,
    );
  }
}
