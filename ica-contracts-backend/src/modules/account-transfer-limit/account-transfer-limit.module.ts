import { Module } from '@nestjs/common';
import { ApisModule } from 'src/apis/apis.module';
import { SharedModule } from 'src/shared/shared.module';

import { AccountTransferLimitController } from './controllers/account-tranfer-limit.controller';
import { AddExtraDailyLimitService } from './services/add-extra-daily-limit.service';
import { DiscountAccountLimitService } from './services/discount-limit.service';
import { GetAccountLimitService } from './services/get-account-limit.service';
import { UpdateAccountLimitService } from './services/update-account-limit.service';
import { UpdateGeneralLimitService } from './services/update-general-transfer-limit.service';
import { UpdateAccountLimitsJobService } from './services/update-limits-job.service';
import { UpdateManyLimitService } from './services/update-many-limits.service';

@Module({
  controllers: [AccountTransferLimitController],
  exports: [DiscountAccountLimitService, GetAccountLimitService],
  providers: [
    UpdateAccountLimitService,
    GetAccountLimitService,
    UpdateGeneralLimitService,
    UpdateAccountLimitsJobService,
    UpdateManyLimitService,
    AddExtraDailyLimitService,
    DiscountAccountLimitService,
  ],
  imports: [SharedModule, ApisModule],
})
export class AccountTransferLimitModule {}
