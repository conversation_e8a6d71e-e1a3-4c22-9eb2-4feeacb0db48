import { Module } from '@nestjs/common';
import { ApisModule } from 'src/apis/apis.module';
// import { BullmqModule } from 'src/bullmq/bullmq.module';
import { SharedModule } from 'src/shared/shared.module';

import { ProcessScheduledPixService } from './process-scheduled-pix.service';

@Module({
  imports: [
    // BullmqModule,
    // BullModule.registerQueue({ name: 'transactions' }),
    ApisModule,
    SharedModule,
  ],
  providers: [ProcessScheduledPixService],
  exports: [],
})
export class MessagingModule {}
