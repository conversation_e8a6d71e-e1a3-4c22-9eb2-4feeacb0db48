name: Code Quality

on:
  pull_request:
    types: [opened, synchronize, reopened]
    branches: [ main, master, develop ]

jobs:
  validate:
    name: Validate Code
    runs-on: ubuntu-latest
    
    permissions:
      contents: read
      pull-requests: write

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'yarn'
          
      - name: Install dependencies
        run: yarn install --frozen-lockfile
        
      - name: Check ESLint
        run: yarn eslint . --ext .ts
        
      - name: Check Prettier
        run: yarn prettier --check "**/*.{ts,js,json,md}"
        
      - name: Type Check
        run: yarn tsc --noEmit

      - name: Build Check
        run: yarn build

      - name: Update PR Status
        if: always()
        uses: actions/github-script@v7
        with:
          script: |
            const conclusion = process.env.JOB_STATUS === 'success' ? 'success' : 'failure';
            await github.rest.checks.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              name: 'Code Quality Check',
              head_sha: context.sha,
              status: 'completed',
              conclusion: conclusion,
              output: {
                title: conclusion === 'success' ? 'All checks passed!' : 'Some checks failed',
                summary: conclusion === 'success' 
                  ? '✅ All code quality checks passed successfully'
                  : '❌ Some code quality checks failed. Please check the logs for details.'
              }
            });
        env:
          JOB_STATUS: ${{ job.status }} 